using System;
using Services;
using UnityEngine;
#if ENABLE_INPUT_SYSTEM
using UnityEngine.InputSystem;
#endif

public class StarterAssetsInputs : MonoBehaviour
{
    [Header("Character Input Values")]
    public Vector2 move;
    public Vector2 look;
    public bool jump;
    public bool sprint;
    public bool grab;
    public float grabPointAdjust;

    public Action<float> OnGrabPointAdjust;

    [<PERSON><PERSON>("Movement Settings")] public bool analogMovement;

    [<PERSON><PERSON>("Mouse Cursor Settings")] public bool cursorLocked = true;
    public bool cursorInputForLook = true;

    private bool _canMove = true;

    public bool CanMove
    {
        get => _canMove;
        set
        {
            _canMove = value;
            move = Vector2.zero;
        }
    }

    private bool _canLook = true;

    public bool CanLook
    {
        get => _canLook;
        set
        {
            _canLook = value;
            look = Vector2.zero;
        }
    }

    private void Awake()
    {
        ServiceLocator.Register(this);
    }

#if ENABLE_INPUT_SYSTEM
    public void OnMove(InputValue value)
    {
        if (!CanMove) return;
        MoveInput(value.Get<Vector2>());
    }

    public void OnLook(InputValue value)
    {
        if (!CanLook) return;
        if (cursorInputForLook)
        {
            LookInput(value.Get<Vector2>());
        }
    }

    public void OnJump(InputValue value)
    {
        JumpInput(value.isPressed);
    }

    public void OnSprint(InputValue value)
    {
        SprintInput(value.isPressed);
    }

    public void OnGrab(InputValue value)
    {
        GrabInput(value.isPressed);
    }

    public void OnMoveGrabPoint(InputValue value)
    {
        MoveGrabPointInput(value.Get<Vector2>());
    }
#endif


    public void MoveInput(Vector2 newMoveDirection)
    {
        move = newMoveDirection;
    }

    public void LookInput(Vector2 newLookDirection)
    {
        look = newLookDirection;
    }

    public void JumpInput(bool newJumpState)
    {
        jump = newJumpState;
    }

    public void SprintInput(bool newSprintState)
    {
        sprint = newSprintState;
    }

    public void GrabInput(bool newGrabState)
    {
        grab = newGrabState;
    }

    private void MoveGrabPointInput(Vector2 movement)
    {
        grabPointAdjust = Mathf.Clamp(movement.y, -1f, 1f) * 0.1f;
        OnGrabPointAdjust?.Invoke(grabPointAdjust);
    }

    private void OnApplicationFocus(bool hasFocus)
    {
        SetCursorState(cursorLocked);
    }

    private void SetCursorState(bool newState)
    {
        Cursor.lockState = newState ? CursorLockMode.Locked : CursorLockMode.None;
    }
}