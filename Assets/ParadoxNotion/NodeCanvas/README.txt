-------------------------------------------------------------
QUICK START
-------------------------------------------------------------
1. To get started, all you have to do is add either 'BehaviourTreeOwner', or 'FSMOwner' component on the gameobject you want to control.
2. Press 'Create New' in the inspector of that added component.
3. Select 'Bound' in the dialog box that will pop up.
4. The editor is now open. You can start creating your Behaviour Tree, or FSM!
(For the Dialogue Tree System, add the 'DialogueTreeController' on a preferrably empty gameobject)


-------------------------------------------------------------
FULL DOCUMENTATION
-------------------------------------------------------------
For the Full Documentation please visit:
https://nodecanvas.paradoxnotion.com/documentation/


-------------------------------------------------------------
CHANGE LOG
-------------------------------------------------------------
You can find the complete change log online at:
https://nodecanvas.paradoxnotion.com/change-log/


-------------------------------------------------------------
RESOURCES
-------------------------------------------------------------
For 3rd Party Integrations, Examples, and other Resources:
https://nodecanvas.paradoxnotion.com/downloads/


-------------------------------------------------------------
UPDATING
-------------------------------------------------------------
To update NodeCanvas you should preferrably:
- Backup your project.
- Remove previous "ParadoxNotion/CanvasCore" and "ParadoxNotion/NodeCanvas" folders.
- Re-import NodeCanvas package anew.


-------------------------------------------------------------
AOT PLATFORMS (eg iOS, xBox)
-------------------------------------------------------------
To make NodeCanvas work with these platforms, please open "Tools/ParadoxNotion/NodeCanvas/Preferred Types Editor".
Then press the "Generate AOTClasses and link.xml Files" button in there to automaticaly create the required files.
All *struct* types with which you are working with within NodeCanvas, should be in that list before generating the file.
You can of course re-generate the file any time you wish.


-------------------------------------------------------------
THIRD PARTY NOTICES
-------------------------------------------------------------
This package uses a modified version of 'Full Serializer' under the MIT License.
See "..ParadoxNotion/CanvasCore/Common/Runtime/Serialization/Full Serializer/LICENSE (FullSerializer).txt".


-------------------------------------------------------------
Thank you for using NodeCanvas!
Gavalakis Evangelos
https://nodecanvas.paradoxnotion.com
-------------------------------------------------------------