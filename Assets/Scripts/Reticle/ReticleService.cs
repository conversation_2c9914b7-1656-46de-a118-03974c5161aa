using Services;
using UnityEngine;

namespace UI
{
    public class ReticleService : MonoBehaviour
    {
        private Reticle _reticle;

        private void Awake()
        {
            ServiceLocator.Register(this);

            _reticle = GetComponent<Reticle>();
        }

        public void SetReticleInteractable(bool interactable)
        {
            _reticle.IsOverInteractable = interactable;
        }
    }
}