using System;
using Math;
using DG.Tweening;
using Interaction;
using Services;
using Shapes;
using UnityEngine;

namespace UI
{
    public class Reticle : MonoBehaviour
    {
        private const float BASE_RADIUS = 0.1f;
        private const float BASE_THICKNESS = 0.4f;
        private const float INTERACTABLE_RADIUS = 0.55f;
        private const float INTERACTABLE_THICKNESS = 0.1f;
        private const float BUSY_RADIUS = 0.1f;
        private const float BUSY_THICKNESS = 0.2f;
        private const float TRANSITION_DURATION = 0.25f;

        private static readonly Color ACTIVE_COLOR = Color.white;
        private static readonly Color INACTIVE_COLOR = new Color(1,1,1, 0.2f);
        private static readonly Color BUSY_COLOR = new Color(0.2f,0.6f,1, 0.6f);
        
        private Disc _disc;

        private Tweener _tween;

        private bool _isOverInteractable;
        
        public bool IsOverInteractable
        {
            get => _isOverInteractable;
            set
            {
                if (value == _isOverInteractable)
                    return;
                
                _isOverInteractable = value;

                if (_isOverInteractable)
                    EnterInteractableState();
                else
                    LeaveInteractableState();
            }
        }
        
        InteractionService _interactionService;
        
        private InteractionService InteractionService
        {
            get
            {
                if (_interactionService == null)
                    _interactionService = ServiceLocator.Locate<InteractionService>();

                return _interactionService;
            }
        }

        private void Awake()
        {
            _disc = GetComponent<Disc>();
        }

        private void Start()
        {
            InteractionService.OnInteractionStateChanged += OnInteractionStateChanged;
        }

        private void OnInteractionStateChanged(bool isBusyInteracting)
        {
            if (isBusyInteracting)
            {
                EnterBusyState();
            }
            else
            {
                LeaveInteractableState();
            }
        }

        private void EnterInteractableState()
        {
            if(InteractionService.IsBusyInteracting)
                return;
            
            KillIfActive();
            
            var currentDiscRadius = _disc.Radius;
            var currentProgress = Floats.CalculateProgress(currentDiscRadius, BASE_RADIUS, INTERACTABLE_RADIUS);
            var duration = Floats.CalculateRemainingDuration(TRANSITION_DURATION, currentProgress);
            _tween = DOTween.To(()=> currentProgress, x=>
            {
                _disc.Radius = Floats.GetValueFromProgress(x, BASE_RADIUS, INTERACTABLE_RADIUS);
                _disc.Thickness = Floats.GetValueFromProgress(x, BASE_THICKNESS, INTERACTABLE_THICKNESS);
                _disc.Color = Color.Lerp(_disc.Color, ACTIVE_COLOR, x);
            }, 1, duration).SetEase(Ease.InOutCubic);
        }

        private void LeaveInteractableState()
        { 
            KillIfActive();
            
            var currentDiscRadius = _disc.Radius;
            var currentProgress = Floats.CalculateProgress(currentDiscRadius, INTERACTABLE_RADIUS, BASE_RADIUS);
            var duration = Floats.CalculateRemainingDuration(TRANSITION_DURATION, currentProgress);
            _tween = DOTween.To(()=> currentProgress, x=>
            {
                _disc.Radius = Floats.GetValueFromProgress(x, INTERACTABLE_RADIUS, BASE_RADIUS);
                _disc.Thickness = Floats.GetValueFromProgress(x, INTERACTABLE_THICKNESS, BASE_THICKNESS);
                _disc.Color = Color.Lerp(_disc.Color, INACTIVE_COLOR, x);
            }, 1, duration).SetEase(Ease.InOutCubic);
        }

        private void EnterBusyState()
        {
            KillIfActive();
            
            var currentDiscRadius = _disc.Radius;
            var currentProgress = Floats.CalculateProgress(currentDiscRadius, INTERACTABLE_RADIUS, BUSY_RADIUS);
            var duration = Floats.CalculateRemainingDuration(TRANSITION_DURATION, currentProgress);
            _tween = DOTween.To(()=> currentProgress, x=>
            {
                _disc.Radius = Floats.GetValueFromProgress(x, INTERACTABLE_RADIUS, BUSY_RADIUS);
                _disc.Thickness = Floats.GetValueFromProgress(x, INTERACTABLE_RADIUS, BUSY_THICKNESS);
                _disc.Color = Color.Lerp(_disc.Color, BUSY_COLOR, x);
            }, 1, duration).SetEase(Ease.InOutCubic);
        }

        private void KillIfActive()
        {
            if (_tween.IsActive() && _tween.IsPlaying())
            {
                _tween.Kill();
            }
        }
    }
}