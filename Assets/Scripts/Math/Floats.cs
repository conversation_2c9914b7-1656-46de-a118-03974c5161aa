using UnityEngine;

namespace Math
{
    public static class Floats
    {
        /// <summary>
        /// Remaps a value from one range to another.
        /// </summary>
        /// <param name="value">The value to remap.</param>
        /// <param name="fromMin">The minimum of the original range.</param>
        /// <param name="fromMax">The maximum of the original range.</param>
        /// <param name="toMin">The minimum of the target range.</param>
        /// <param name="toMax">The maximum of the target range.</param>
        /// <returns>The remapped value.</returns>
        public static float Remap(float value, float fromMin, float fromMax, float toMin, float toMax)
        {
            return (value - fromMin) / (fromMax - fromMin) * (toMax - toMin) + toMin;
        }
        
        /// <summary>
        /// Calculates the remaining duration of an event given the total duration and the current progress.
        /// </summary>
        /// <param name="totalDuration">The total duration of the event.</param>
        /// <param name="currentProgress">The current progress of the event (0 to 1).</param>
        /// <returns>The remaining duration of the event.</returns>
        public static float CalculateRemainingDuration(float totalDuration, float currentProgress)
        {
            // Clamp current progress between 0 and 1 to ensure valid input
            currentProgress = Mathf.Clamp01(currentProgress);

            // Calculate the remaining duration
            float remainingDuration = totalDuration * (1f - currentProgress);

            return remainingDuration;
        }
        
        /// <summary>
        /// Calculates the current progress of a value between a start and end value.
        /// </summary>
        /// <param name="currentValue">The current value.</param>
        /// <param name="startValue">The start value.</param>
        /// <param name="endValue">The end value.</param>
        /// <returns>The current progress as a value between 0 and 1.</returns>
        public static float CalculateProgress(float currentValue, float startValue, float endValue)
        {
            // Avoid division by zero and ensure startValue is not equal to endValue
            if (startValue == endValue)
            {
                Debug.LogWarning("Start and End values are equal. Returning progress as 0.");
                return 0f;
            }

            // Calculate progress as a value between 0 and 1
            return (currentValue - startValue) / (endValue - startValue);
        }
        
        /// <summary>
        /// Calculates the value within a range given the progress between start and end values.
        /// </summary>
        /// <param name="progress">The progress value (0 to 1).</param>
        /// <param name="startValue">The start value of the range.</param>
        /// <param name="endValue">The end value of the range.</param>
        /// <returns>The interpolated value at the given progress.</returns>
        public static float GetValueFromProgress(float progress, float startValue, float endValue)
        {
            // Linear interpolation between startValue and endValue based on progress
            return Mathf.Lerp(startValue, endValue, progress);
        }
    }
}