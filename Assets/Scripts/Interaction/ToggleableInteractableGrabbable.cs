public abstract class ToggleableInteractableGrabbable : InteractableGrabbableObject
{
    protected override void OnInteract()
    {
        if (IsBeingInteractedWith)
        {
            OnEndInteraction();
            return;
        }

        base.OnInteract();
        
        InteractionService.StartInteraction();
    }

    protected override void OnEndInteraction()
    {
        base.OnEndInteraction();
        
        InteractionService.EndInteraction();
    }
}