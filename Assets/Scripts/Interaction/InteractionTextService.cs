using DG.Tweening;
using Math;
using Services;
using TMPro;
using UnityEngine;

namespace Interaction
{
    public class InteractionTextService : MonoBehaviour
    {
        private const float TRANSITION_DURATION = 0.2f;
        
        private CanvasGroup InteractionTextCanvas;
        private TextMeshProUGUI InteractionText;

        private Tweener _tween;
        
        private void Awake()
        {
            ServiceLocator.Register(this);

            InteractionTextCanvas = GetComponent<CanvasGroup>();
            InteractionText = GetComponentInChildren<TextMeshProUGUI>();
        }

        private void Start()
        {
            HideInteraction();
        }

        public void DisplayInteraction(string interactionString)
        {
            InteractionText.text = interactionString;
            
            KillIfActive();

            var currentCanvasAlpha = InteractionTextCanvas.alpha;
            var duration = Floats.CalculateRemainingDuration(TRANSITION_DURATION, currentCanvasAlpha);
            _tween = DOTween.To(()=> currentCanvasAlpha, x=>
                InteractionTextCanvas.alpha = x, 1, duration);
        }

        public void HideInteraction()
        {
            KillIfActive();
            
            var currentCanvasAlpha = InteractionTextCanvas.alpha;
            var duration = TRANSITION_DURATION - Floats.CalculateRemainingDuration(TRANSITION_DURATION, currentCanvasAlpha);
            _tween = DOTween.To(()=> currentCanvasAlpha, x=>
                InteractionTextCanvas.alpha = x, 0, duration).OnComplete(() => 
                InteractionText.text = string.Empty);
        }

        private void KillIfActive()
        {
            if (_tween.IsActive() && _tween.IsPlaying())
            {
                _tween.Kill();
            }
        }
    }
}