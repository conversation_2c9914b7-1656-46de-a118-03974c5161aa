using UnityEngine;

[RequireComponent(typeof(Rigidbody))]
public class WorldPositionResetter : MonoBehaviour
{
    private Rigidbody _Rigidbody;

    private float _heightOffGround;

    private void Awake()
    {
        _Rigidbody = GetComponent<Rigidbody>();

        if(_Rigidbody == null)
        {
            _Rigidbody = gameObject.GetComponentInChildren<Rigidbody>();
        }

        var totalSize = new Bounds(_Rigidbody.centerOfMass, Vector3.zero);
        
        // get combined height of all colliders' bounds attached to this rigidbody
        foreach (var collider in _Rigidbody.GetComponentsInChildren<Collider>())
        {
            totalSize.Encapsulate(collider.bounds);
        }
        
        
        _heightOffGround = totalSize.size.y;
    }
    
    public void ReturnToWorldPosition()
    {
        var wasKinematic = _Rigidbody.isKinematic;
        
        _Rigidbody.isKinematic = true;
        
        transform.position = Vector3.zero + Vector3.up * _heightOffGround;
        transform.rotation = Quaternion.identity;
        
        _Rigidbody.isKinematic = wasKinematic;
    }
}