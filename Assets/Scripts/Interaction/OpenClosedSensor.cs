using System;
using UnityEngine;
using UnityEngine.Events;

public class OpenClosedSensor : MonoBehaviour
{
    [SerializeField] Vector3 InitialDirection;
    [SerializeField] float MinDotProduct = 0.5f;
 
    public UnityEvent OnOpened;
    public UnityEvent OnClosed;
    
    private bool _isOpened;
    private bool IsOpened
    {
        get => _isOpened;
        set
        {
            if (_isOpened == value)
                return;
            
            _isOpened = value;
            if (_isOpened)
                OnOpened?.Invoke();
            else
                OnClosed?.Invoke();
        }
    }
    
    private void Start()
    {
        InitialDirection = transform.forward;
    }

    private void Update()
    {
        var dotProduct = Vector3.Dot(transform.forward, InitialDirection);
        IsOpened = dotProduct < MinDotProduct;
    }

    private void OnDrawGizmos()
    {
        Gizmos.color = IsOpened ? Color.green : Color.red;
        Gizmos.DrawLine(transform.position, transform.position + transform.forward);
        Gizmos.color = Color.blue;
        Gizmos.DrawLine(transform.position, transform.position + InitialDirection);
    }
}
