using Interaction;
using Services;
using UnityEngine;
using UnityEngine.InputSystem;

public class PhysicsGrabber : MonoBehaviour
{
    private const float DEFAULT_SPRING_JOINT_MASS_SCALE = 10f;

    [Header("Grab Settings")]
    [Tooltip("Distance in front of the player where grabbed objects will be held")]
    public float holdDistance = 1.0f;
    public float baseHoldDistance;
    [Toolt<PERSON>("Radius of the spherecast used to detect objects")]
    public float grabRadius = 0.1f;
    [Toolt<PERSON>("Maximum distance at which objects can be grabbed")]
    public float maxGrabDistance = 5.0f;
    [Tooltip("Force applied to move the object to the grab point")]
    public float grabStrength = 20.0f;
    [Tooltip("How quickly the object's velocity is dampened")]
    public float dampingFactor = 5.0f;
    [Tooltip("Maximum distance before the connection breaks")]
    public float maxBreakDistance = 7.0f;
    [Tooltip("Maximum force before the connection breaks")]
    public float maxBreakForce = 20.0f;
    [Tooltip("Layers that can be grabbed")]
    public LayerMask grabbableLayers = ~0;

    [Header("References")]
    [Tooltip("Transform where grabbed objects will be held")]
    public Transform grabPoint;

    // Private variables
    private Camera _mainCamera;
    private Rigidbody _grabbedObject;
    private IInteractable _interactingObject;
    private SpringJoint _springJoint;
    private bool _isGrabbing = false;
    private Vector3 _grabOffset; // Offset from object center to hit point
    private Vector3 _hitPoint; // The exact point where the ray hit the object
    private GrabbableObject _grabbableComponent;
    private Vector3 _previousVelocity;
    private PlayerInput _playerInput;
    
    private CharacterController _characterController;

    private IInteractable InteractingObject
    {
        get => _interactingObject;
        set
        {
            var previousInteractingObject = _interactingObject;
            _interactingObject = value;
            
            if (_interactingObject is ToggleableInteractableGrabbableMouse)
            {
                _playerInput.SwitchCurrentActionMap("Mouse");
            }
            else if (previousInteractingObject is ToggleableInteractableGrabbableMouse)
            {
                _playerInput.SwitchCurrentActionMap("Player");
            }

            if (_interactingObject != null)
            {
                _interactingObject.InteractionEnded += OnInteractionEnded;
            }
        }
    }

    private InteractionService _interactionService;
    
    private InteractionService InteractionService
    {
        get
        {
            if (_interactionService == null)
                _interactionService = ServiceLocator.Locate<InteractionService>();

            return _interactionService;
        }
    }
    
    private PlayerInputActions _playerInputActions;
    private PlayerInputActions PlayerInputActions
    {
        get
        {
            if (_playerInputActions == null)
            {
                _playerInputActions = new PlayerInputActions();
                ServiceLocator.Register(_playerInputActions);
            }
            
            return _playerInputActions;
        }
    }

    private void Awake()
    {
        _mainCamera = Camera.main;
        _characterController = GetComponent<CharacterController>();
        _playerInput = GetComponent<PlayerInput>();

        // Create grab point if not assigned
        if (grabPoint == null)
        {
            GameObject grabPointObj = new GameObject("GrabPoint");
            grabPoint = grabPointObj.transform;
            grabPoint.SetParent(transform);
            grabPoint.localPosition = new Vector3(0, 0, holdDistance);
        }
        
        baseHoldDistance = holdDistance;
        
        PlayerInputActions.Player.Grab.started += OnGrabInputStarted;
        PlayerInputActions.Player.Grab.canceled += OnGrabInputReleased;
        PlayerInputActions.Player.MoveGrabPoint.performed += OnGrabPointAdjust;
        PlayerInputActions.Player.Interact.performed += OnInteractPerformed;
    }

    private void OnDestroy()
    {
        PlayerInputActions.Player.Grab.started -= OnGrabInputStarted;
        PlayerInputActions.Player.Grab.canceled -= OnGrabInputReleased;
        PlayerInputActions.Player.MoveGrabPoint.performed -= OnGrabPointAdjust;
        PlayerInputActions.Player.Interact.performed -= OnInteractPerformed;
    }

    private void OnEnable()
    {
        PlayerInputActions.Enable();
    }

    private void OnDisable()
    {
        PlayerInputActions.Disable();
    }

    private void Update()
    {
        // Update grab point position
        UpdateGrabPoint();
        
        RaycastHit hit;

        if (SphereCast(out hit))
        {
            var gameObjectName = hit.collider.GetComponentInParent<Rigidbody>().gameObject.name;
            InteractionService.DisplayInteraction(gameObjectName);
        }
        else
        {
            InteractionService.HideInteraction();
        }
        
        // Check if we need to break the connection
        if (_isGrabbing)
        {
            CheckBreakConnection();
        }
    }

    private void FixedUpdate()
    {
        if (!_isGrabbing || _grabbedObject == null)
            return;
        
        ForceBasedMovement();
    }

    private void OnGrabInputStarted(InputAction.CallbackContext obj)
    {
        if (!_isGrabbing)
        {
            TryGrab();
        }
    }
    
    private void OnGrabInputReleased(InputAction.CallbackContext obj)
    {
        if (_isGrabbing)
        {
            Release();
        }
    }

    private void OnGrabPointAdjust(InputAction.CallbackContext ctx)
    {
        if(!_isGrabbing)
            return;

        Vector2 scrollDelta = ctx.ReadValue<Vector2>() * 0.1f;
        holdDistance = Mathf.Clamp(holdDistance + scrollDelta.y, baseHoldDistance * 0.5f, baseHoldDistance * 2.5f);
        UpdateGrabPoint();
    }

    private void OnInteractPerformed(InputAction.CallbackContext obj)
    {
        if (InteractingObject is ToggleableInteractableGrabbable currentInteractingObject)
        {
            currentInteractingObject.EndInteraction();
            InteractingObject = null;
            return;
        }

        IInteractable targetInteractable;
        
        if (_isGrabbing && _grabbableComponent is InteractableGrabbableObject interactable)
        {
            targetInteractable = interactable;
        }
        else
        {
            RaycastHit hit;

            if (!SphereCast(out hit))
                return;

            hit.collider.attachedRigidbody.TryGetComponent(out IInteractable interactableObject);
            
            targetInteractable = interactableObject;
        }

        if (targetInteractable == null)
            return;
            
        targetInteractable.Interact();
        InteractingObject = targetInteractable;
    }

    private void OnInteractionEnded(IInteractable interactable)
    {
        interactable.InteractionEnded -= OnInteractionEnded;
        InteractingObject = null;
    }

    private void ForceBasedMovement()
    {
        // Always use the center of the screen as the desired position
        var desiredHitPointPosition = _mainCamera.transform.position + _mainCamera.transform.forward * holdDistance;

        // Check if we need to maintain a fixed distance
        if (_grabbableComponent != null && _grabbableComponent.ShouldMaintainFixedDistance())
        {
            var fixedDistance = _grabbableComponent.GetInitialGrabDistance();
            var direction = (_mainCamera.transform.forward).normalized;
            desiredHitPointPosition = _mainCamera.transform.position + direction * fixedDistance;
        }

        // Calculate where the object center needs to be for the hit point to be at the desired position
        var targetPos = desiredHitPointPosition - (_grabbedObject.rotation * (_grabOffset));
        
        // Calculate direction and distance to target
        var directionToTarget = targetPos - _grabbedObject.position;
        var distance = directionToTarget.magnitude;

        // Calculate target velocity based on distance
        var targetVelocity = directionToTarget.normalized * Mathf.Min(distance * 10f, 20f);

        // Calculate velocity change needed
        var velocityChange = targetVelocity - _grabbedObject.linearVelocity;

        // Apply force based on desired velocity change
        var currentGrabStrength = grabStrength;
        if (_grabbableComponent != null && _grabbableComponent.GetGrabStrength() > 0)
        {
            currentGrabStrength = _grabbableComponent.GetGrabStrength();
        }
        
        // Apply the force
        _grabbedObject.AddForce(velocityChange * currentGrabStrength, ForceMode.Acceleration);

        // Apply damping to reduce oscillation
        _grabbedObject.AddForce(-_grabbedObject.linearVelocity * dampingFactor, ForceMode.Acceleration);

        // Limit velocity to prevent extreme forces
        if (_grabbedObject.linearVelocity.magnitude > 20f)
        {
            _grabbedObject.linearVelocity = _grabbedObject.linearVelocity.normalized * 20f;
        }
    }
    
    private void UpdateGrabPoint()
    {
        // Update grab point position to be exactly in the center of the screen at the specified distance
        grabPoint.position = _mainCamera.transform.position + _mainCamera.transform.forward * holdDistance;
        
        // Ensure the grab point is always aligned with the camera's forward direction
        grabPoint.rotation = _mainCamera.transform.rotation;
    }

    private void TryGrab()
    {
        RaycastHit hit;

        if (SphereCast(out hit))
        {
            // Check if the hit object has a rigidbody
            Rigidbody rb = hit.collider.attachedRigidbody;

            if (rb == null)
            {
                return;
            }
            
            InteractionService.StartInteraction();
            
            // Start grabbing the object
            _grabbedObject = rb;
            _isGrabbing = true;

            // Store the exact hit point and calculate offset from center in local space
            _hitPoint = hit.point;

            // Debug visualization of hit point
            Debug.DrawLine(_hitPoint, _hitPoint + Vector3.up * 0.2f, Color.red, 2.0f);

            // Check if the object has a GrabbableObject component
            _grabbableComponent = rb.GetComponent<GrabbableObject>();
            if (_grabbableComponent != null)
            {
                _grabbableComponent.StartGrab(grabPoint);
                _grabbableComponent.SetHitPoint(_hitPoint);
                _grabOffset = _grabbableComponent.ShouldUseCenterOfMassAsGrabPoint() ? rb.centerOfMass : _hitPoint - rb.transform.position;
            }
            
            // Convert the grab offset to local space of the object
            _grabOffset = rb.transform.InverseTransformPoint(_hitPoint);
            // _grabOffset = Quaternion.Inverse(rb.rotation) * _grabOffset;
            
            if (!rb.isKinematic)
            {
                // Create a spring joint to connect the object
                CreateSpringJoint();
            }
        }
    }

    private bool SphereCast(out RaycastHit hit)
    {
        // Perform a spherecast from the camera
        Ray ray = _mainCamera.ScreenPointToRay(new Vector3(Screen.width / 2, Screen.height / 2, 0));
        
        return Physics.SphereCast(ray, grabRadius, out hit, maxGrabDistance, grabbableLayers);
    }

    private void CreateSpringJoint()
    {
        // Create a game object to host the spring joint
        GameObject jointObj = new GameObject("GrabJoint");
        
        // Position the joint at the exact hit point instead of the object's center
        jointObj.transform.position = _hitPoint;

        // Add and configure the spring joint
        _springJoint = jointObj.AddComponent<SpringJoint>();
        _springJoint.connectedBody = _grabbedObject;
        _springJoint.anchor = Vector3.zero;
        _springJoint.autoConfigureConnectedAnchor = false;
        _springJoint.connectedAnchor = Vector3.zero;//_grabOffset;

        // Configure spring settings
        _springJoint.spring = 120f;
        _springJoint.damper = 5f;
        _springJoint.massScale = DEFAULT_SPRING_JOINT_MASS_SCALE;
        _springJoint.maxDistance = 2f;

        // Make the joint follow the grab point
        jointObj.transform.SetParent(grabPoint);
        jointObj.transform.localPosition = Vector3.zero;

        // Temporarily disable gravity to make the object easier to control
        _grabbedObject.useGravity = false;

        // Reduce angular drag to make rotation more responsive
        _grabbedObject.angularDamping = 0.5f;
    }

    private void Release()
    {
        InteractionService.EndInteraction();
            
        if (_springJoint != null)
        {
            Destroy(_springJoint.gameObject);
            _springJoint = null;
        }

        if (_grabbedObject != null)
        {
            // Re-enable gravity
            _grabbedObject.useGravity = true;

            // Restore original angular drag
            _grabbedObject.angularDamping = 0.05f;
        }

        // Notify the grabbable component if it exists
        if (_grabbableComponent != null)
        {
            _grabbableComponent.EndGrab();
            _grabbableComponent = null;
        }

        _isGrabbing = false;
        _grabbedObject = null;
        holdDistance = baseHoldDistance;
    }

    private void CheckBreakConnection()
    {
        if (_grabbedObject == null) return;

        // Check distance
        float distance = Vector3.Distance(_grabbedObject.position, grabPoint.position);
        if (distance > maxBreakDistance)
        {
            Release();
            return;
        }

        // Check force/tension (approximated by velocity difference)
        Vector3 velocityDiff = _grabbedObject.linearVelocity - _characterController.velocity;
        if (velocityDiff.magnitude > maxBreakForce)
        {
            Release();
            return;
        }
    }

    private void OnDrawGizmosSelected()
    {
        // Draw grab radius
        Gizmos.color = Color.yellow;
        if (_mainCamera != null)
        {
            Gizmos.DrawWireSphere(_mainCamera.transform.position + _mainCamera.transform.forward * maxGrabDistance, grabRadius);
        }

        // Draw grab point
        if (grabPoint != null)
        {
            Gizmos.color = Color.green;
            Gizmos.DrawWireSphere(grabPoint.position, 0.1f);
        }

        // Draw break distance
        if (_isGrabbing && _grabbedObject != null)
        {
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(grabPoint.position, maxBreakDistance);

            // Draw hit point
            Gizmos.color = Color.blue;
            Gizmos.DrawSphere(_hitPoint, 0.05f);

            // Draw line from object center to hit point
            Gizmos.color = Color.cyan;
            Gizmos.DrawLine(_grabbedObject.position, _hitPoint);

            // Draw line from hit point to grab point
            Gizmos.color = Color.magenta;
            Gizmos.DrawLine(_hitPoint, grabPoint.position);
        }
    }
}
