using System;
using System.Collections.Generic;
using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif

public class ChairSpinner : MonoBehaviour
{
    [<PERSON><PERSON>("Rotation Settings")]
    [Serial<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("How fast the chair rotates based on object movement")]
    private float _rotationSpeed = 10f;
    [Ser<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("How quickly the rotation slows down")]
    private float _rotationDamping = 2f;
    [<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("Minimum movement threshold to cause rotation")]
    private float _movementThreshold = 0.01f;
    [Ser<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("Maximum rotation speed in degrees per second")]
    private float _maxRotationSpeed = 180f;
    [<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("Whether to visualize the movement vectors (for debugging)")]
    private bool _showDebugVisuals = false;
    [SerializeField, Toolt<PERSON>("Whether to rotate in the opposite direction of movement")]
    private bool _invertRotation = false;
    [Serial<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("Whether to lock rotation to the Y axis only")]
    private bool _lockToYAxis = true;

    [Header("Runtime Values")]
    [Ser<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("Current rotation velocity")]
    private float _currentVelocity = 0f;

    private Transform _rotateTarget;

    private SphereCollider _sphereCollider;

    private SphereCollider SphereCollider
    {
        get
        {
            if(_sphereCollider == null)
                _sphereCollider = GetComponent<SphereCollider>();

            return _sphereCollider;
        }
    }

    private Rigidbody _rigidbody;

    private Rigidbody Rigidbody
    {
        get
        {
            if(_rigidbody == null)
                _rigidbody = GetComponentInParent<Rigidbody>();

            return _rigidbody;
        }
    }

    private List<Transform> _trackedObjects = new List<Transform>();

    private Dictionary<Transform, Vector3> _lastPositions = new Dictionary<Transform, Vector3>();

    private void Awake()
    {
        if(!SphereCollider.isTrigger)
            Debug.LogWarning($"{gameObject.name} must have a trigger collider.");

        _trackedObjects = new List<Transform>();
        _lastPositions = new Dictionary<Transform, Vector3>();

        _rotateTarget = transform.parent;
    }

    // rotate along the y-axis based on current velocity
    private void Update()
    {
        // Apply damping
        _currentVelocity *= Mathf.Pow(1 / _rotationDamping, Time.deltaTime);

        // Stop rotation if velocity is very small
        if(Mathf.Abs(_currentVelocity) < 0.1f)
        {
            _currentVelocity = 0f;
            return;
        }

        // Get rotation axis (either world Y or chair's up vector)
        Vector3 rotationAxis = _lockToYAxis ? Vector3.up : _rotateTarget.up;

        // Apply rotation around the chosen axis
        _rotateTarget.Rotate(rotationAxis, _currentVelocity * Time.deltaTime, Space.World);
    }

    private void FixedUpdate()
    {
        foreach (var trackedObject in _trackedObjects)
        {
            if (!_lastPositions.ContainsKey(trackedObject))
                continue;

            var currentPosition = trackedObject.position;
            var lastPosition = _lastPositions[trackedObject];

            // Calculate movement vector
            var movement = currentPosition - lastPosition;

            // Skip tiny movements
            if (movement.magnitude < _movementThreshold)
            {
                _lastPositions[trackedObject] = currentPosition;
                continue;
            }

            // Get chair center and up vector
            Vector3 chairCenter = _rotateTarget.position;
            Vector3 chairUp = _rotateTarget.up;

            // Project positions onto the horizontal plane
            Vector3 chairToLastPos = lastPosition - chairCenter;
            Vector3 chairToCurrentPos = currentPosition - chairCenter;

            // Project vectors onto horizontal plane (perpendicular to chair's up vector)
            chairToLastPos = Vector3.ProjectOnPlane(chairToLastPos, chairUp);
            chairToCurrentPos = Vector3.ProjectOnPlane(chairToCurrentPos, chairUp);

            // Skip if positions are too close to center (would cause unstable calculations)
            if (chairToLastPos.magnitude < 0.1f || chairToCurrentPos.magnitude < 0.1f)
            {
                _lastPositions[trackedObject] = currentPosition;
                continue;
            }

            // Normalize for angle calculation
            chairToLastPos.Normalize();
            chairToCurrentPos.Normalize();

            // Calculate angle between vectors
            float angle = Vector3.SignedAngle(chairToLastPos, chairToCurrentPos, chairUp);

            // Apply rotation based on angle (invert if needed)
            float rotationFactor = _invertRotation ? -1f : 1f;
            var velocityToAdd = angle * _rotationSpeed * Time.fixedDeltaTime * rotationFactor;
            
            AddRotationalForce(velocityToAdd);

            // Debug visualization
            if (_showDebugVisuals)
            {
                Debug.DrawRay(chairCenter, chairToLastPos * 2f, Color.red, 0.1f);
                Debug.DrawRay(chairCenter, chairToCurrentPos * 2f, Color.green, 0.1f);
                Debug.DrawLine(lastPosition, currentPosition, Color.blue, 0.1f);
            }

            _lastPositions[trackedObject] = currentPosition;
        }
    }

    private void OnCollisionEnter(Collision other)
    {
        // Get chair center and up vector
        Vector3 chairCenter = _rotateTarget.position;
        Vector3 chairUp = _rotateTarget.up;
            
        float angle = Vector3.SignedAngle(other.GetContact(0).point, chairCenter, chairUp);
        var velocityToAdd = angle * Rigidbody.linearVelocity.sqrMagnitude;

        AddRotationalForce(velocityToAdd);
    }

    private void OnTriggerEnter(Collider other)
    {
        // Only track objects with rigidbodies or Characters
        if (other.attachedRigidbody != null || other.transform.GetComponent<CharacterController>() != null)
        {
            Transform objectToTrack = other.transform;

            // Avoid duplicates
            if (!_trackedObjects.Contains(objectToTrack))
            {
                _trackedObjects.Add(objectToTrack);
                _lastPositions[objectToTrack] = objectToTrack.position;

                if (_showDebugVisuals)
                    Debug.Log($"Started tracking {objectToTrack.name}");
            }
        }
    }

    private void OnTriggerExit(Collider other)
    {
        if (other.attachedRigidbody != null  || other.transform.GetComponent<CharacterController>() != null)
        {
            Transform objectToTrack = other.transform;

            if (_trackedObjects.Contains(objectToTrack))
            {
                _trackedObjects.Remove(objectToTrack);
                _lastPositions.Remove(objectToTrack);

                if (_showDebugVisuals)
                    Debug.Log($"Stopped tracking {objectToTrack.name}");
            }
        }
    }

    /// <summary>
    /// Manually add rotational force to the chair
    /// </summary>
    /// <param name="force">The amount of rotational force to add</param>
    private void AddRotationalForce(float force)
    {
        _currentVelocity += force;
        _currentVelocity = Mathf.Clamp(_currentVelocity, -_maxRotationSpeed, _maxRotationSpeed);
    }

    /// <summary>
    /// Set the chair's rotation velocity directly
    /// </summary>
    /// <param name="velocity">The new rotation velocity</param>
    public void SetRotationVelocity(float velocity)
    {
        _currentVelocity = Mathf.Clamp(velocity, -_maxRotationSpeed, _maxRotationSpeed);
    }
}
