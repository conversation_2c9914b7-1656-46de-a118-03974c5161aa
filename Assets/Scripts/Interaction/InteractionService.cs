using System;
using Services;
using UI;
using UnityEngine;

namespace Interaction
{
    public class InteractionService : MonoBehaviour
    {
        public Action<bool> OnInteractionStateChanged;
        
        private InteractionTextService _interactionTextService;

        private InteractionTextService InteractionTextService
        {
            get
            {
                if (_interactionTextService == null)
                    _interactionTextService = ServiceLocator.Locate<InteractionTextService>();

                return _interactionTextService;
            }
        }
        
        ReticleService _reticleService;
        
        private ReticleService ReticleService
        {
            get
            {
                if (_reticleService == null)
                    _reticleService = ServiceLocator.Locate<ReticleService>();

                return _reticleService;
            }
        }
        
        private bool _isBusyInteracting = false;
        
        public bool IsBusyInteracting
        {
            get => _isBusyInteracting;
            private set
            {
                if (value == _isBusyInteracting)
                    return;
                
                _isBusyInteracting = value;
                
                if (_isBusyInteracting)
                {
                    HideInteraction();
                }
                
                OnInteractionStateChanged?.Invoke(_isBusyInteracting);
            }
        }

        private void Awake()
        {
            ServiceLocator.Register(this);
        }
        
        public void StartInteraction()
        {
            IsBusyInteracting = true;
        }

        public void EndInteraction()
        {
            IsBusyInteracting = false;
        }

        public void DisplayInteraction(string interactionString)
        {
            if(IsBusyInteracting)
                return;
            
            ReticleService.SetReticleInteractable(true);
            InteractionTextService.DisplayInteraction(interactionString);
        }

        public void HideInteraction()
        {
            ReticleService.SetReticleInteractable(false);
            InteractionTextService.HideInteraction();
        }
    }
}