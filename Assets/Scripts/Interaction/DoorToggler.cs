using System;
using Interaction;
using Services;
using UnityEngine;

public class DoorToggler : InteractableGrabbableObject
{
    private bool _isOpen = false;
    private float _closeTargetVelocity;
    private float _openTargetVelocity;
    
    private HingeJoint _hingeJoint;
    private HingeJoint HingeJoint
    {
        get
        {
            if (_hingeJoint == null)
                _hingeJoint = GetComponent<HingeJoint>();

            return _hingeJoint;
        }
    }
    
    private void Awake()
    {
        _closeTargetVelocity = HingeJoint.motor.targetVelocity;
        _openTargetVelocity = -HingeJoint.motor.targetVelocity * 3;
    }
    
    protected override void OnInteract()
    {
        base.OnInteract();

        SetHingeJointMotorDirection(!_isOpen);
    }
    
    protected override void OnGrabStart()
    {
        base.OnGrabStart();
        SetHingeJointMotorDirection(false);
    }

    private void SetHingeJointMotorDirection(bool isOpen)
    {
        if(isOpen == _isOpen)
            return;
        
        _isOpen = isOpen;
        
        var motor = HingeJoint.motor;
        motor.targetVelocity = _isOpen ? _openTargetVelocity : _closeTargetVelocity;
        HingeJoint.motor = motor;
    }
}
