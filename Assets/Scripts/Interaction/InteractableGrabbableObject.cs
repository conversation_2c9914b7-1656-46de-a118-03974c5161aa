using System;
using Interaction;
using Services;
using UnityEngine;

public abstract class InteractableGrabbableObject : GrabbableObject, IInteractable
{
    public Action<IInteractable> InteractionStarted { get; set; }
    public Action<IInteractable> InteractionEnded { get; set; }
    public bool IsBeingInteractedWith { get; set; }
    
    private InteractionService _interactionService;

    public InteractionService InteractionService
    {
        get
        {
            if (_interactionService == null)
                _interactionService = ServiceLocator.Locate<InteractionService>();

            return _interactionService;
        }
    }

    public void Interact()
    {
        OnInteract();
    }

    public void EndInteraction()
    {
        OnEndInteraction();
    }

    protected virtual void OnInteract()
    {
        Debug.Log($"Interacted with {name}");
        InteractionStarted?.Invoke(this);
        IsBeingInteractedWith = true;
    }

    protected virtual void OnEndInteraction()
    {
        Debug.Log($"Ended interaction with {name}");
        InteractionEnded?.Invoke(this);
        IsBeingInteractedWith = false;
    }
}