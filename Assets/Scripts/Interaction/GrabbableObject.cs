using System;
using UnityEngine;

[RequireComponent(typeof(Rigidbody))]
public class GrabbableObject : MonoBehaviour
{
    [Head<PERSON>("Grab Settings")]
    [Tooltip("Custom grab strength for this object (0 = use default from PhysicsGrabber)")]
    public float customGrabStrength = 0f;
    [Tooltip("Whether this object should align with the grab point rotation")]
    public bool alignWithGrabRotation = false;
    [Tooltip("How quickly the object aligns with the grab rotation")]
    public float rotationSpeed = 5f;
    [Tooltip("Whether to disable gravity while grabbed")]
    public bool disableGravityWhenGrabbed = true;
    [Tooltip("Whether to freeze rotation while grabbed")]
    public bool freezeRotationWhenGrabbed = false;
    [Tooltip("Whether to maintain a fixed distance from the grab point")]
    public bool maintainFixedDistance = false;
    [Tooltip("Whether to use the center of mass as the grab point")]
    public bool useCenterOfMassAsGrabPoint = false;
    
    public Action GrabStarted;
    public Action GrabEnded;

    private WorldPositionResetter _worldPositionResetter;
    protected Rigidbody _Rigidbody;
    private bool _isGrabbed = false;
    private Transform _grabPoint;
    private bool _wasUsingGravity;
    private bool _wasKinematic;
    private RigidbodyConstraints _originalConstraints;
    private float _initialGrabDistance;
    private Vector3 _hitPoint; // The exact point where the ray hit the object

    private float _initialMass;
    private float _initialDamping;
    private float _initialAngularDamping;

    private void Awake()
    {
        _Rigidbody = GetComponent<Rigidbody>();

        if(_Rigidbody == null)
        {
            _Rigidbody = gameObject.GetComponentInChildren<Rigidbody>();
        }
        
        _worldPositionResetter = gameObject.AddComponent<WorldPositionResetter>();
        
        _initialMass = _Rigidbody.mass;
        _initialDamping = _Rigidbody.linearDamping;
        _initialAngularDamping = _Rigidbody.angularDamping;
    }

    private void Update()
    {
        CheckAndResetPosition();
    }
    
    private void FixedUpdate()
    {
        if (_isGrabbed && _grabPoint != null)
        {
            // Handle rotation alignment if enabled
            if (alignWithGrabRotation)
            {
                // Gradually align with grab point rotation
                Quaternion targetRotation = _grabPoint.rotation;
                _Rigidbody.MoveRotation(Quaternion.Slerp(_Rigidbody.rotation, targetRotation, rotationSpeed * Time.fixedDeltaTime));
            }

            // If we need to maintain a fixed distance
            if (maintainFixedDistance)
            {
                // Calculate the direction from grab point to object
                Vector3 direction = (transform.position - _grabPoint.position).normalized;

                // Calculate the target position at the fixed distance
                Vector3 targetPosition = _grabPoint.position + direction * _initialGrabDistance;

                // Apply a small force to maintain the distance
                Vector3 forceDirection = targetPosition - transform.position;
                _Rigidbody.AddForce(forceDirection * 5f, ForceMode.Acceleration);
            }
        }
    }

    public void StartGrab(Transform grabPoint)
    {
        _isGrabbed = true;
        _grabPoint = grabPoint;

        // Store initial distance to maintain if needed
        _initialGrabDistance = Vector3.Distance(transform.position, grabPoint.position);

        if (_Rigidbody != null)
        {
            // Store original rigidbody settings
            _wasUsingGravity = _Rigidbody.useGravity;
            _wasKinematic = _Rigidbody.isKinematic;
            _originalConstraints = _Rigidbody.constraints;
            
            _Rigidbody.mass = _initialMass / 10f;
            _Rigidbody.linearDamping = 5f;
            _Rigidbody.angularDamping = 5f;

            // Apply grab-specific rigidbody settings
            if (disableGravityWhenGrabbed)
            {
                _Rigidbody.useGravity = false;
            }

            if (freezeRotationWhenGrabbed)
            {
                _Rigidbody.constraints = RigidbodyConstraints.FreezeRotation;
            }
        }
        
        OnGrabStart();
    }

    public void EndGrab()
    {
        _isGrabbed = false;
        _grabPoint = null;

        if(_Rigidbody != null)
        {
            // Restore original rigidbody settings
            _Rigidbody.useGravity = _wasUsingGravity;
            _Rigidbody.isKinematic = _wasKinematic;
            _Rigidbody.constraints = _originalConstraints;
            
            // Reset rigidbody mass and damping
            _Rigidbody.mass = _initialMass;
            _Rigidbody.linearDamping = _initialDamping;
            _Rigidbody.angularDamping = _initialAngularDamping;
        }
        
        OnGrabEnd();
    }
    
    protected virtual void OnGrabStart()
    {
        GrabStarted?.Invoke();
    }

    protected virtual void OnGrabEnd()
    {
        GrabEnded?.Invoke();
    }

    private void CheckAndResetPosition()
    {
        if(_Rigidbody == null)
            return;
        
        // Check if the object is moving
        if (_Rigidbody?.linearVelocity.magnitude < 0.01f)
            return;

        // check if the object's position is outside the play space
        if (_worldPositionResetter != null)
        {
            var wasOutsidePlaySpace = _worldPositionResetter.transform.position.sqrMagnitude > 1000f;
            if (wasOutsidePlaySpace)
            {
                _worldPositionResetter.ReturnToWorldPosition();
            }
        }
    }

    // Get custom grab settings or return 0 if default should be used
    public float GetGrabStrength() => customGrabStrength;
    public float GetInitialGrabDistance() => _initialGrabDistance;
    public bool ShouldMaintainFixedDistance() => maintainFixedDistance;
    public bool ShouldUseCenterOfMassAsGrabPoint() => useCenterOfMassAsGrabPoint;

    // Set the hit point where the ray collided with the object
    public void SetHitPoint(Vector3 hitPoint)
    {
        _hitPoint = hitPoint;
    }

    // Get the hit point
    public Vector3 GetHitPoint() => _hitPoint;
}