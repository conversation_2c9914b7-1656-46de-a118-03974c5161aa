using Data;
using UnityEngine;

namespace InGameMouse
{
    public class MouseUIUpdater : MonoBehaviour
    {
        [SerializeField] private RangedVector2Variable MousePosition;
        private RectTransform _mouseUI;

        private void Awake()
        {
            MousePosition.OnValueChanged += UpdateUI;
            _mouseUI = (RectTransform)transform;
        }

        private void UpdateUI(Vector2 vector2)
        {
            _mouseUI.anchoredPosition = vector2;
        }
    }
}