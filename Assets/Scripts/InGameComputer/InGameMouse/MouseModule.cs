using Data;
using Services;
using UnityEngine;
using UnityEngine.InputSystem;

namespace InGameMouse
{
    public class MouseModule : MonoBehaviour
    {
        [SerializeField] private RangedVector2Variable MousePosition;
        [SerializeField] private float MouseSensitivity = 6800f;
        [SerializeField] private float MouseModeSensitivity = 50f;
        [SerializeField] private float DetectionDistance = 0.05f;
    
        private Vector3 _lastMousePosition;
        private Vector3 _currentMousePosition;
        private bool _isInMouseMode;

        private PlayerInputActions _playerInputActions;
        private PlayerInputActions PlayerInputActions
        {
            get
            {
                if (_playerInputActions == null)
                    _playerInputActions = ServiceLocator.Locate<PlayerInputActions>();
            
                return _playerInputActions;
            }
        }

        private void Awake()
        {
            ServiceLocator.Register(this);
        }

        private void Start()
        {
            PlayerInputActions.Mouse.MouseMovement.performed += MouseMovementOnPerformed;
        }

        private void Update()
        {
            if (_isInMouseMode)
            {
                return;
            }
            
            Vector3 raycastStartOffset = transform.up * DetectionDistance/2;
            Ray ray = new Ray(transform.position + raycastStartOffset, -transform.up);
            RaycastHit hit;
            if (Physics.Raycast(ray, out hit, DetectionDistance))
            {
                if (_lastMousePosition == Vector3.zero)
                    _lastMousePosition = hit.point;
                
                UpdatePhysicsMousePosition(hit.point);
            }
            else
            {
                _lastMousePosition = Vector3.zero;
            }
        }
        
        public void EnterMouseMode()
        {
            _isInMouseMode = true;
        }
        
        public void ExitMouseMode()
        {
            _isInMouseMode = false;
        }

        private void MouseMovementOnPerformed(InputAction.CallbackContext ctx)
        {
            if (!_isInMouseMode)
            {
                return;
            }
            
            var mouseDelta = ctx.ReadValue<Vector2>();
            MousePosition.Value += mouseDelta * MouseModeSensitivity;
        }

        private void UpdatePhysicsMousePosition(Vector3 hitPoint)
        {
            _currentMousePosition = hitPoint;

            if (Mathf.Approximately(_currentMousePosition.sqrMagnitude, _lastMousePosition.sqrMagnitude))
                return;
        
            // add the difference to the mouse position
            var mouseDelta = (_currentMousePosition - _lastMousePosition) * MouseSensitivity;
            var mouseDelta2D = new Vector2(mouseDelta.x, mouseDelta.z);
            
            // translate to local coordinates
            mouseDelta2D = transform.InverseTransformDirection(mouseDelta2D);
        
            MousePosition.Value += mouseDelta2D;
        
            _lastMousePosition = _currentMousePosition;
        }
    }
}
