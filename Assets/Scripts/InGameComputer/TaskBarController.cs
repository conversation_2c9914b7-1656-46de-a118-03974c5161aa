using System.Collections.Generic;
using Data;
using DG.Tweening;
using Services;
using UnityEngine;

public class TaskBarController : MonoBehaviour
{
    [SerializeField] private RangedVector2Variable MousePosition;
    
    [SerializeField] private Transform TaskBarItemContainer;
    [SerializeField] private GameObject TaskBarItemPrefab;
    
    private CanvasGroup _taskBarCanvasGroup;
    private RectTransform _taskBar;
    
    private List<ApplicationLauncherHelper> _taskBarItems = new();
    
    private int CurrentTaskBarScreenHeight => IsBeingHovered ? 100 : 50;

    private bool FullscreenApplicationIsFocused => false;
    
    private bool _isBeingHovered = false;
    private bool IsBeingHovered
    {
        get => _isBeingHovered;
        set
        {
            if (_isBeingHovered == value)
                return;
            
            _isBeingHovered = value;
            UpdateTaskBarVisibility();
        }
    }
    
    private bool IsVisible => !FullscreenApplicationIsFocused || IsBeingHovered;

    private void Awake()
    {
        _taskBarCanvasGroup = GetComponent<CanvasGroup>();
        _taskBar = GetComponent<RectTransform>();
    }

    private void Start()
    {
        MousePosition.OnValueChanged += CheckMousePosition;
        
        ServiceLocator.Locate<ApplicationManager>().OnApplicationLaunched += OnApplicationLaunched;
    }

    private void OnApplicationLaunched(App newlyLaunchedApp)
    {
        var taskBarItem = Instantiate(TaskBarItemPrefab, TaskBarItemContainer).GetComponent<ApplicationLauncherHelper>();
        taskBarItem.name = $"TaskBarItem_{newlyLaunchedApp.ApplicationData.Name}";
        taskBarItem.Setup(newlyLaunchedApp);
        _taskBarItems.Add(taskBarItem);
    }

    private void CheckMousePosition(Vector2 mousePosition)
    {
        var atRequiredYValue = mousePosition.y <= CurrentTaskBarScreenHeight;
        IsBeingHovered = atRequiredYValue;
    }

    private void UpdateTaskBarVisibility()
    {
        _taskBarCanvasGroup.DOFade(IsVisible ? 1 : 0, 0.5f);
        _taskBar.DOAnchorPosY(IsVisible ? _taskBar.rect.height : 0, 0.5f);
    }
}
