using Services;
using UnityEngine;

public class ComputerMouseService : MonoBehaviour
{
    private VirtualMouseInput _virtualMouseInput;
    
    private void Awake()
    {
        ServiceLocator.Register(this);
        _virtualMouseInput = GetComponent<VirtualMouseInput>();
        Enable();
    }

    public void Enable()
    {
        _virtualMouseInput.enabled = true;
        Show();
    }
    
    public void Disable()
    {
        _virtualMouseInput.enabled = false;
        Hide();
    }

    private void Show()
    {
        _virtualMouseInput.gameObject.SetActive(true);
    }

    private void Hide()
    {
        _virtualMouseInput.gameObject.SetActive(false);
    }
}
