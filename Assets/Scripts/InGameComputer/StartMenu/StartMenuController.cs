using System.Collections.Generic;
using System.Linq;
using DG.Tweening;
using Services;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace InGameComputer.StartMenu
{
    public class StartMenuController : MonoBehaviour
    {
        private const float MENU_FADE_DURATION = 0.1f;
        [SerializeField] private CanvasGroup StartMenu;
        [SerializeField] private GameObject StartMenuApplicationButtonPrefab;

        private GenericInAppActionableButton _inAppButton;
        
        private List<GameObject> _applicationButtons = new List<GameObject>();

        private bool IsOpen { get; set; } = false;

        private ApplicationManager _applicationManager;
        private List<ApplicationData> _applicationsAlphabetized;

        private ApplicationManager ApplicationManager
        {
            get
            {
                if (_applicationManager == null)
                    _applicationManager = ServiceLocator.Locate<ApplicationManager>();

                return _applicationManager;
            }
        }
        
        private void Awake()
        {
            _inAppButton = GetComponent<GenericInAppActionableButton>();

            _inAppButton.OnButtonPressed += OnStartButtonPressed;
        }

        private void Start()
        {
            Close();

            _applicationsAlphabetized = ApplicationManager.Applications.Keys.ToList().OrderBy(a => a.Name).ToList();
            
            CreateApplicationButtons();
        }

        private void CreateApplicationButtons()
        {
            foreach (var applicationData in _applicationsAlphabetized)
            {
                var button = Instantiate(StartMenuApplicationButtonPrefab, StartMenu.transform);
                var applicationLauncherHelper = button.GetComponent<ApplicationLauncherHelper>();
                applicationLauncherHelper.Setup(ApplicationManager.Applications[applicationData]);
                
                _applicationButtons.Add(button);
                
                button.transform.GetChild(0).GetComponent<Image>().sprite = applicationData.Icon;
                button.GetComponentInChildren<TextMeshProUGUI>().text = applicationData.Name;
            }
        }

        private void OnStartButtonPressed(GenericInAppActionableButton _)
        {
            Toggle();
        }

        private void Toggle()
        {
            IsOpen = !IsOpen;
            
            if (IsOpen)
                Open();
            else
                Close();
        }
        
        private void Open()
        {
            IsOpen = true;
            
            _applicationButtons.ForEach(button => button.SetActive(true));

            StartMenu.DOFade(1, MENU_FADE_DURATION);
            StartMenu.interactable = true;
            StartMenu.blocksRaycasts = true;
        }
        
        private void Close()
        {
            IsOpen = false;
            
            _applicationButtons.ForEach(button => button.SetActive(false));

            StartMenu.DOFade(0, MENU_FADE_DURATION);
            StartMenu.interactable = false;
            StartMenu.blocksRaycasts = false;
        }
    }
}
