using DG.Tweening;
using UnityEngine;

public abstract class TimedApplet : Applet
{
    public bool IsStarted
    {
        get;
        protected set;
    } = false;

    private float Duration { get; }

    public float TimeRemaining
    {
        get;
        private set;
    }

    private float Progress => TimeRemaining / Duration;
    
    public bool IsRunning => IsStarted && Progress < 1;
    
    protected TimedApplet(App app, CanvasGroup menuCanvasGroup, float duration) : base(app, menuCanvasGroup)
    {
        Duration = duration;
        TimeRemaining = Duration;
    }

    protected void StartTimer()
    {
        if (IsStarted)
            return;
        
        IsStarted = true;

        DOVirtual.Float(0, Duration, Duration, OnTimerUpdated).OnComplete(OnTimerCompleted);
    }

    protected virtual void OnTimerUpdated(float timeElapsed)
    {
        TimeRemaining = Duration - timeElapsed;
    }

    protected abstract void OnTimerCompleted();
}