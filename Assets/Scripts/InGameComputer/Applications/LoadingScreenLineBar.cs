using UnityEngine.UI;

public class LoadingScreenLineBar : LoadingScreen
{
    private Slider _loadingBar;
    private Slider LoadingBar
    {
        get
        {
            if (_loadingBar == null)
                _loadingBar = GetComponentInChildren<Slider>();

            return _loadingBar;
        }
    }
    
    protected override void UpdateLoadingProgress()
    {
        if (LoadingBar == null)
            return;
        
        LoadingBar.value = LoadingProgress;
    }
}