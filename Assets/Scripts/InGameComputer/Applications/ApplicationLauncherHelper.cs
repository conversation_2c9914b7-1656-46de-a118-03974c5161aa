using Services;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

public class ApplicationLauncherHelper : MonoBehaviour
{
    [FormerlySerializedAs("_applicationData")] [SerializeField]
    private ApplicationData ApplicationData;
    
    private Image _iconImage;
    
    private Image IconImage
    {
        get
        {
            if (_iconImage == null)
                _iconImage = GetButtonImage();

            return _iconImage;
        }
    }

    private ApplicationManager _applicationManager;

    private ApplicationManager ApplicationManager
    {
        get
        {
            if (_applicationManager == null)
                _applicationManager = ServiceLocator.Locate<ApplicationManager>();

            return _applicationManager;
        }
    }

    public void Setup(App newlyLaunchedApp)
    {
        ApplicationData = newlyLaunchedApp.ApplicationData;
        
        if (ApplicationData == null)
            Debug.LogError($"ApplicationData is null on {name}", this);

        if (ApplicationData.Icon != null)
        {
            IconImage.sprite = ApplicationData.Icon;
        }
    }

    public void ToggleApplication()
    {
        
    }

    public void LaunchApplication()
    {
        ApplicationManager.OpenApplication(ApplicationData);
    }

    private Image GetButtonImage()
    {
        transform.GetChild(0).TryGetComponent(out Image image);
        return image;
    }
}