using System;
using DG.Tweening;
using UnityEngine;
using Random = UnityEngine.Random;

public abstract class LoadingScreen : MonoBehaviour
{
    public Action OnLoadingEnded;

    #region Properties

    private CanvasGroup _canvasGroup;
    private CanvasGroup CanvasGroup
    {
        get
        {
            if (_canvasGroup == null)
                _canvasGroup = GetComponent<CanvasGroup>();

            return _canvasGroup;
        }
    }
    
    private float _loadingProgress = 0f;

    protected float LoadingProgress
    {
        get => _loadingProgress;
        set
        {
            if (Mathf.Approximately(_loadingProgress, value))
                return;
            
            _loadingProgress = value;
            UpdateLoadingProgress();
        }
    }

    public bool IsActive { get; private set; }

    #endregion

    private float _randomLoadingTime = 0f;
    private readonly float randomLoadingTimeRange = 5f;
    
    private void Awake()
    {
        transform.SetAsLastSibling();
    }

    public void SkipLoading()
    {
        EndLoading();
    }

    public void BeginLoading()
    {
        if (IsActive)
            return;

        IsActive = true;
        
        CanvasGroup.blocksRaycasts = true;
        _randomLoadingTime = Random.Range(5, randomLoadingTimeRange);
        
        DOVirtual.Float(0, 100, _randomLoadingTime, (x) => LoadingProgress = x).OnComplete(EndLoading);
    }

    private void EndLoading()
    {
        LoadingProgress = 1;
        CanvasGroup.blocksRaycasts = false;
        IsActive = false;
        
        CanvasGroup.DOFade(0, 0.5f);
        
        OnLoadingEnded?.Invoke();
    }

    protected abstract void UpdateLoadingProgress();
}