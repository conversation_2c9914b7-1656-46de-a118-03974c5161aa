using System.Collections.Generic;
using InGameComputer.Applications;
using UnityEngine.Events;

public class GenericInAppActionableButton : InAppActionableButton<GenericInAppActionableButton>
{
    public List<UnityEvent> OnButtonPressedEvents;
    
    protected override void Initialize() {}

    protected override void OnClick()
    {
        OnButtonPressed?.Invoke(this);
        
        OnButtonPressedEvents.ForEach(e => e.Invoke());
    }
}