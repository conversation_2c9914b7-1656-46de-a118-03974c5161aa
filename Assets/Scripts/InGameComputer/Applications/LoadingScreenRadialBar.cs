using UnityEngine.UI;

public class LoadingScreenRadialBar : LoadingScreen
{
    private Image _loadingBar;
    private Image LoadingBar
    {
        get
        {
            if (_loadingBar == null)
                _loadingBar = transform.GetChild(0).GetComponent<Image>();

            return _loadingBar;
        }
    }

    protected override void UpdateLoadingProgress()
    {
        if (LoadingBar == null)
            return;
        
        LoadingBar.fillAmount = LoadingProgress / 100;
    }
}