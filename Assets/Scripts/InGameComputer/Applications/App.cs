using System;
using DG.Tweening;
using InGameComputer.Applications.AimTrainer;
using Services;
using UnityEngine;

public class App : MonoBehaviour, IApp
{
    public Action ApplicationStarted;

    [SerializeField] public bool IsRunning = false;
    [SerializeField] protected ApplicationData _applicationData;
    
    #region Properties

    public ApplicationData ApplicationData => _applicationData;

    public Applet Applet
    {
        get;
        protected set;
    }

    private CanvasGroup _appCanvasGroup;

    private CanvasGroup AppCanvasGroup
    {
        get
        {
            if (_appCanvasGroup == null)
                _appCanvasGroup = GetComponent<CanvasGroup>();

            return _appCanvasGroup;
        }
    }

    public CanvasGroup MenuCanvasGroup;

    private LoadingScreen _loadingScreen;

    private LoadingScreen LoadingScreen
    {
        get
        {
            if (_loadingScreen == null)
                _loadingScreen = GetComponentInChildren<LoadingScreen>();

            return _loadingScreen;
        }
    }

    private bool _isOpen = false;

    public bool IsOpen
    {
        get => _isOpen;
        private set
        {
            if (_isOpen == value)
                return;

            _isOpen = value;
            if (_isOpen)
                OnOpened();
            else
                OnClosed();
        }
    }
    
    private ApplicationManager _applicationManager;
    
    private ApplicationManager ApplicationManager
    {
        get
        {
            if (_applicationManager == null)
                _applicationManager = ServiceLocator.Locate<ApplicationManager>();

            return _applicationManager;
        }
    }

    private bool IsDoneLoading => IsRunning && !LoadingScreen.IsActive;

    #endregion

    private void Awake()
    {
        if (LoadingScreen != null)
        {
            LoadingScreen.OnLoadingEnded += OnLoadingEnded;
        }

        SetupApplet();
    }

    protected virtual void SetupApplet()
    {
        switch (_applicationData.AppType)
        {
            case AppType.AimTrainer:
                Applet = new AimTrainer(this, MenuCanvasGroup, 20);
                break;
            case AppType.Idler:
                Applet = new Idler(this, MenuCanvasGroup);
                break;
            default:
                Debug.Log($"Unknown App Type on {_applicationData.Name}", this);
                break;
        }
    }

    private void Start()
    {
        if (IsRunning)
        {
            Open();
        }
        else
        {
            AppCanvasGroup.alpha = 0;
            AppCanvasGroup.gameObject.SetActive(false);
        }
    }

    private void OnLoadingEnded()
    {
        StartApplication();
    }

    private void StartApplication()
    {
        Debug.Log($"Starting {ApplicationData.Name}");
        ApplicationStarted?.Invoke();
    }
    
    private void FocusApplication()
    {
        ApplicationManager.OpenApplication(_applicationData);
    }

    public void Open()
    {
        IsOpen = true;
    }

    public void Close()
    {
        IsOpen = false;
    }

    protected virtual void OnOpened()
    {
        AppCanvasGroup.DOFade(1, 0.5f).OnStart(() => AppCanvasGroup.gameObject.SetActive(true));

        if (IsRunning)
            FocusApplication();
        else
        {
            if (IsDoneLoading)
                StartApplication();
            else
                LoadingScreen.BeginLoading();
        }

        IsRunning = true;
    }

    protected virtual void OnClosed()
    {
        AppCanvasGroup.DOFade(0, 0.5f).OnComplete(() => AppCanvasGroup.gameObject.SetActive(false));
    }
}