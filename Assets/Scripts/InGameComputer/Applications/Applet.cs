using DG.Tweening;
using UnityEngine;

public abstract class Applet
{
    private readonly App _app;
    
    public string Name => _app.ApplicationData.Name;
    public string Description => _app.ApplicationData.Description;

    private CanvasGroup MainMenuCanvasGroup { get; }

    protected Applet(App app, CanvasGroup menuCanvasGroup)
    {
        _app = app;
        MainMenuCanvasGroup = menuCanvasGroup;
    }

    protected void ShowMenu()
    {
        SetMenuActive(true);
        MainMenuCanvasGroup.DOFade(1, 0.5f);
        OnMenuShown();
    }

    private void SetMenuActive(bool isActive)
    {
        MainMenuCanvasGroup.gameObject.SetActive(isActive);
    }

    protected void HideMenu()
    {
        MainMenuCanvasGroup.DOFade(0, 0.25f).OnComplete(() => SetMenuActive(false));
        OnMenuHidden();
    }

    protected virtual void OnMenuShown() { }

    protected virtual void OnMenuHidden() { }

    public abstract void StartApplet();
}