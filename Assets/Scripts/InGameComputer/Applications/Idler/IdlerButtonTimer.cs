using System;
using UnityEngine;
using UnityEngine.UI;

public class IdlerButtonTimer : MonoBehaviour
{
    public Action OnTimerFinished;
    
    [SerializeField] private float TimerDuration = 2f;
    
    private Image _timerImage;
    
    private bool _timerHasBegun;
    private float _elapsedTime;
    
    public bool IsTimerRunning => _timerHasBegun && _elapsedTime < TimerDuration;
    public float ElapsedProgress => _elapsedTime / TimerDuration;

    private void Start()
    {
        _timerImage = GetComponent<Image>();
        _timerImage.fillAmount = 0f;
    }

    private void Update()
    {
        if (!_timerHasBegun)
            return;
        
        _elapsedTime += Time.deltaTime;
        if (!IsTimerRunning)
        {
            FinishTimer();
            return;
        }
        
        UpdateTimerVisuals(ElapsedProgress);
    }

    private void UpdateTimerVisuals(float fillAmount)
    {
        _timerImage.fillAmount = fillAmount;
    }

    public bool StartTimer()
    {
        if (IsTimerRunning)
            return false;

        _elapsedTime = 0f;
        _timerHasBegun = true;
        
        return true;
    }

    private void FinishTimer()
    {
        _timerHasBegun = false;
        OnTimerFinished?.Invoke();
        UpdateTimerVisuals(0f);
    }
}