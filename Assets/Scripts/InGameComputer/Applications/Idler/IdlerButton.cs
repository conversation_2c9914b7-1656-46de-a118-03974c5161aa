using UnityEngine;

public class IdlerButton : InAppButton
{
    private IdlerButtonTimer _idlerButtonTimer;

    protected override void Initialize()
    {
        _idlerButtonTimer = GetComponentInChildren<IdlerButtonTimer>();
    }

    protected override void OnClick()
    {
        TryStartTimer();
    }

    private void TryStartTimer()
    {
        var timerStartedSuccessfully = _idlerButtonTimer.StartTimer();
        
        if (!timerStartedSuccessfully)
        {
            Debug.Log("Timer is already running");
            return;
        }
    }
}