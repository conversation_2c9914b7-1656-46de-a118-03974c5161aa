using UnityEngine;

namespace InGameComputer.Applications.AimTrainer
{
    public class AimTrainerButton : InAppActionableButton<AimTrainerButton>
    {
        private AimTrainer _aimTrainer;
    
        RectTransform _rectTransform;
    
        Vector2 _position;

        public Vector2 Position
        {
            get => _rectTransform.anchoredPosition;
            set
            {
                _position = value;
                _rectTransform.anchoredPosition = _position;
            }
        }

        protected override void Initialize()
        {
            _rectTransform = (RectTransform) transform;
            _aimTrainer = GetComponentInParent<App>().Applet as AimTrainer;

            if (_aimTrainer != null)
                _aimTrainer.RegisterButton(this);
        }

        protected override void OnClick()
        {
            if(!_aimTrainer.IsRunning)
                return;
        
            OnButtonPressed?.Invoke(this);
        }
    }
}