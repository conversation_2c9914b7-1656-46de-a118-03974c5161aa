using System;
using UnityEngine;
using Random = UnityEngine.Random;

namespace InGameComputer.Applications.AimTrainer
{
    public class AimTrainer : TimedApplet
    {
        public Action OnAppletStarted;
        public Action<int> OnScoreChanged;
    
        private const int BUTTON_PADDING = 80;
        private const int MAX_X_POSITION = 1840;
        private const int MAX_Y_POSITION = 1000;
    
        private int _score;

        private int Score
        {
            get => _score;
            set
            {
                if (!IsRunning)
                    return;
            
                _score = value;
            
                OnScoreChanged?.Invoke(_score);
            }
        }

        public AimTrainer(App app, CanvasGroup menuCanvasGroup, int timeLimit) : base(app, menuCanvasGroup, timeLimit)
        {
            ResetScore();
        }

        public override void StartApplet()
        {
            if (IsStarted)
                return;
        
            StartTimer();
            HideMenu();
            OnAppletStarted?.Invoke();
        }

        protected override void OnTimerCompleted()
        {
            IsStarted = false;

            Debug.Log($"Final Score: {Score}");
            ResetScore();
        
            ShowMenu();
        }

        private void ResetScore()
        {
            _score = 0;
        }

        private void ButtonPressed(AimTrainerButton aimTrainerButton)
        {
            Score += 1;

            Debug.Log($"Score: {Score}");
        
            aimTrainerButton.Position = GetRandomButtonPosition();
        }

        private Vector2 GetRandomButtonPosition()
        {
            var nextButtonPosition = GetRandomPosition();
        
            if( nextButtonPosition.x > MAX_X_POSITION )
                nextButtonPosition.x = BUTTON_PADDING;
            if( nextButtonPosition.y > MAX_Y_POSITION )
                nextButtonPosition.y = BUTTON_PADDING;
        
            return nextButtonPosition;
        }

        private Vector2 GetRandomPosition()
        {
            return new Vector2(Random.Range(BUTTON_PADDING, MAX_X_POSITION), Random.Range(BUTTON_PADDING, MAX_Y_POSITION));
        }

        public void RegisterButton(AimTrainerButton aimTrainerButton)
        {
            aimTrainerButton.OnButtonPressed += ButtonPressed;
            SetInitialButtonPosition(aimTrainerButton);
        }

        private void SetInitialButtonPosition(AimTrainerButton aimTrainerButton)
        {
            aimTrainerButton.Position = GetRandomButtonPosition();
        }
    }
}