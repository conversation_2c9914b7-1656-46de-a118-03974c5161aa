using TMPro;
using UnityEngine;

namespace InGameComputer.Applications.AimTrainer
{
    public class AimTrainerScoreUpdater : MonoBehaviour
    {
        private AimTrainer _aimTrainer;
        private TextMeshProUGUI _textMeshProUGUI;

        private void Awake()
        {
            _textMeshProUGUI = GetComponent<TextMeshProUGUI>();
            _aimTrainer = GetComponentInParent<CustomMouseApp>().Applet as AimTrainer;
        }
    
        private void Start()
        {
            _aimTrainer.OnAppletStarted += ClearScore;
            _aimTrainer.OnScoreChanged += UpdateScore;
        }
    
        private void OnDestroy()
        {
            _aimTrainer.OnAppletStarted -= ClearScore;
            _aimTrainer.OnScoreChanged -= UpdateScore;
        }
    
        private void ClearScore()
        {
            Debug.Log("Score Cleared");
            _textMeshProUGUI.text = "0";
        }

        private void UpdateScore(int score)
        {
            Debug.Log($"Score Updated: {score}");
            _textMeshProUGUI.text = score.ToString();
        }
    }
}
