using DG.Tweening;
using UnityEngine;

public class AppletView : MonoBehaviour
{
    private App _app;
    
    private CanvasGroup _appContentCanvasGroup;
    private CanvasGroup AppContentCanvasGroup
    {
        get
        {
            if (_appContentCanvasGroup == null)
                _appContentCanvasGroup = GetComponent<CanvasGroup>();

            return _appContentCanvasGroup;
        }
    }

    private void Awake()
    {
        _app = GetComponentInParent<App>();
        _app.ApplicationStarted += OnApplicationStarted;
    }

    private void Start()
    {
        if(!_app.IsRunning)
            AppContentCanvasGroup.alpha = 0;
    }

    protected virtual void OnApplicationStarted()
    {
        OpenMainMenu();
    }

    protected virtual void OpenMainMenu()
    {
        AppContentCanvasGroup.DOFade(1, 0.5f);
    }
}