using System;
using System.Collections.Generic;
using Services;
using UnityEngine;

public class ApplicationManager : MonoBehaviour
{
    public Action<App> OnApplicationLaunched;
    
    private Dictionary<ApplicationData, App> _applications = new Dictionary<ApplicationData, App>();
    [SerializeField] private List<ApplicationData> ApplicationDataList = new List<ApplicationData>();
    [SerializeField] private List<App> ApplicationList = new List<App>();
    
    private List<App> _openApplications = new List<App>();
    public List<App> OpenApplications => _openApplications;
    
    public Dictionary<ApplicationData, App> Applications => _applications;
    
    private App _currentApplication;
    private App CurrentApplication
    {
        get => _currentApplication;
        set
        {
            if (_currentApplication == value)
                return;
            
            if (_currentApplication != null)
                _currentApplication.Close();
            
            _currentApplication = value;
            
            if (_currentApplication != null)
                ShowApplication(_currentApplication);
        }
    }
    
    private void Awake()
    {
        ServiceLocator.Register(this);
        
        for (var i = 0; i < ApplicationDataList.Count; i++)
        {
            _applications.Add(ApplicationDataList[i], ApplicationList[i]);
        }
    }

    public void OpenApplication(ApplicationData applicationData)
    {
        var app = _applications[applicationData];
        
        CurrentApplication = app;
        
        if (OpenApplications.Contains(app))
        {
            Debug.Log($"Opening {applicationData.Name}");

            return;
        }
        
        Debug.Log($"Launching {applicationData.Name}");
        
        OpenApplications.Add(app);
        OnApplicationLaunched?.Invoke(app);
    }
    
    public void CloseApplication(App app)
    {
        if (!OpenApplications.Contains(app))
            return;
        
        OpenApplications.Remove(app);
        app.Close();
        
        if (CurrentApplication == app)
            CurrentApplication = null;
    }

    private void ShowApplication(App app)
    {
        app.transform.SetSiblingIndex(_applications.Count - 1);
        app.Open();
    }
}