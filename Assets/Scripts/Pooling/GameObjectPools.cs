using System.Collections.Generic;
using System.Linq;
using Services;
using UnityEngine;

namespace Pooling
{
    public class GameObjectPools : MonoBehaviour
    {
        private Dictionary<string, GameObject[]> _projectilePools;

        private void Awake()
        {
            ServiceLocator.Register(this);
            _projectilePools = new Dictionary<string, GameObject[]>();
        }

        public void AddPool(string poolName, GameObject prefab, int count = 100)
        {
            if (_projectilePools.ContainsKey(poolName))
            {
                Debug.LogWarning($"A pool with the name {poolName} already exists");
                return;
            }
            
            var projectiles = new GameObject[count];

            var poolContainer = new GameObject(poolName)
            {
                transform =
                {
                    parent = transform
                }
            };

            for (int i = 0; i < count; i++)
            {
                var newProjectile = Instantiate(prefab, poolContainer.transform);
                newProjectile.SetActive(false);
                
                projectiles[i] = newProjectile;
            }
            
            _projectilePools.Add(poolName, projectiles);
        }

        public GameObject[] GetPool(string poolName)
        {
            return _projectilePools[poolName];
        }

        public GameObject GetNextAvailableFromPool(string poolName)
        {
            var targetPool = GetPool(poolName);

            return targetPool?.FirstOrDefault(poolItem => !poolItem.activeSelf);
        }
    }
}