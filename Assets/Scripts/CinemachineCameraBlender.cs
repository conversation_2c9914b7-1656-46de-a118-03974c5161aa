using System;
using UnityEngine;
using System.Collections;
using Services;
using Unity.Cinemachine;

public class CinemachineBlender : MonoBehaviour
{
    [<PERSON>lt<PERSON>("First Cinemachine virtual camera")]
    public CinemachineCamera virtualCameraA;
    
    [Tooltip("Second Cinemachine virtual camera")]
    public CinemachineCamera virtualCameraB;
    
    [Tooltip("How long the blend should take")]
    public float blendDuration = 2.0f;
    
    [Tooltip("Curve to control the blend transition")]
    public AnimationCurve blendCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
    
    [Range(0, 1)]
    [Tooltip("Blend value (0 = camera A, 1 = camera B)")]
    public float blendAmount = 0;
    
    private float currentBlendAmount = 0;
    private Coroutine blendCoroutine;
    
    // Default priority values
    private int defaultPriorityA;
    private int defaultPriorityB;
    
    // The priority offset to ensure proper blending
    private const int PRIORITY_OFFSET = 10;

    private void Awake()
    {
        ServiceLocator.Register(this);
    }

    private void Start()
    {
        if (virtualCameraA == null || virtualCameraB == null)
        {
            Debug.LogError("Both virtual cameras must be assigned!");
            enabled = false;
            return;
        }
        
        // Store the default priorities
        defaultPriorityA = virtualCameraA.Priority;
        defaultPriorityB = virtualCameraB.Priority;
        
        // Initialize the cameras based on the initial blend amount
        UpdateCameraPriorities(blendAmount);
    }

#if UNITY_EDITOR
    private void Update()
    {
        // If blend amount changed in inspector, update the blend
        if (currentBlendAmount != blendAmount)
        {
            BlendTo(blendAmount);
        }
    }
#endif
    
    /// <summary>
    /// Blend to a specific target value
    /// </summary>
    /// <param name="targetBlendAmount">Target blend value (0 = camera A, 1 = camera B)</param>
    public void BlendTo(float targetBlendAmount)
    {
        // Clamp the value between 0 and 1
        targetBlendAmount = Mathf.Clamp01(targetBlendAmount);
        
        // If already blending, stop the current blend
        if (blendCoroutine != null)
        {
            StopCoroutine(blendCoroutine);
        }
        
        // Start a new blend
        blendCoroutine = StartCoroutine(BlendCameras(targetBlendAmount));
    }
    
    /// <summary>
    /// Coroutine to smoothly blend between cameras
    /// </summary>
    private IEnumerator BlendCameras(float targetBlendAmount)
    {
        float startBlendAmount = currentBlendAmount;
        float startTime = Time.time;
        
        while (Time.time < startTime + blendDuration)
        {
            // Calculate how far we are through the blend
            float t = (Time.time - startTime) / blendDuration;
            
            // Apply the animation curve for smoother transitions
            t = blendCurve.Evaluate(t);
            
            // Calculate the current blend amount
            currentBlendAmount = Mathf.Lerp(startBlendAmount, targetBlendAmount, t);
            
            // Update the priorities of the cameras
            UpdateCameraPriorities(currentBlendAmount);
            
            // Update the inspector value
            blendAmount = currentBlendAmount;
            
            yield return null;
        }
        
        // Ensure we reach the exact target blend amount
        currentBlendAmount = targetBlendAmount;
        blendAmount = targetBlendAmount;
        UpdateCameraPriorities(currentBlendAmount);
        
        blendCoroutine = null;
    }
    
    /// <summary>
    /// Update the priorities of both cameras based on the blend amount
    /// </summary>
    private void UpdateCameraPriorities(float blend)
    {
        // As blend goes from 0 to 1, camera A's priority decreases and camera B's increases
        if (blend <= 0)
        {
            // Fully on camera A
            virtualCameraA.Priority = defaultPriorityA + PRIORITY_OFFSET;
            virtualCameraB.Priority = defaultPriorityB;
        }
        else if (blend >= 1)
        {
            // Fully on camera B
            virtualCameraA.Priority = defaultPriorityA;
            virtualCameraB.Priority = defaultPriorityB + PRIORITY_OFFSET;
        }
        else
        {
            // In between - calculate priorities based on the blend value
            // This approach ensures Cinemachine's internal blending handles the transition
            virtualCameraA.Priority = blend < 0.5f ? 
                defaultPriorityA + PRIORITY_OFFSET : 
                defaultPriorityA;
                
            virtualCameraB.Priority = blend >= 0.5f ? 
                defaultPriorityB + PRIORITY_OFFSET : 
                defaultPriorityB;
        }
    }
    
    /// <summary>
    /// Convenience method to instantly switch to camera A
    /// </summary>
    public void SwitchToCameraA()
    {
        BlendTo(0);
    }
    
    /// <summary>
    /// Convenience method to instantly switch to camera B
    /// </summary>
    public void SwitchToCameraB()
    {
        BlendTo(1);
    }
}