using DG.Tweening;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class UpdateDownloader : MonoBehaviour
{
    private Color _progressColor = new Color(0.1f, 0.6f, 1f);
    private Color _progressPulseColor = new Color(0.1f, 0.5f, 1f);
    private Color _completeColor = new Color(0.1f, 0.7f, 0.1f);
    
    [SerializeField] private Slider UpdateProgressSlider;
    [SerializeField] private Image UpdateProgressSliderFill;
    [SerializeField] private TextMeshProUGUI UpdateProgressPercentage;
    
    [SerializeField] private float PulseDuration = 1f;
    
    Animator _animator;
    AnimationClip _animationClip;
    
    private float _percentComplete;
    private bool IsDownloadComplete => Mathf.Approximately(_percentComplete, 100f);
    
    Tweener _pulseTween;
    Sequence _pulseSequence;

    private void Awake()
    {
        _animator = GetComponentInChildren<Animator>();
        
        _animationClip = _animator.runtimeAnimatorController.animationClips[0];
    }

    private void Start()
    {
        Restart();
    }

    private void Update()
    {
        if(IsDownloadComplete)
            return;

        if (!Mathf.Approximately(UpdateProgressSlider.value, _percentComplete))
        {
            _percentComplete = UpdateProgressSlider.value;
            UpdatePercentage(_percentComplete);
        }
        
        if (IsDownloadComplete)
        {
            FinishDownload();
        }
    }

    private void Restart()
    {
        Reset();
        StartDownload();
    }

    private void StartDownload()
    {
        _animator.Play(_animationClip.name);
        
        UpdateProgressSliderFillColor(_progressColor);
        
        _pulseTween = DOVirtual.Color(_progressColor, _progressPulseColor, PulseDuration, UpdateProgressSliderFillColor).SetEase(Ease.InOutQuad).SetLoops(-1, LoopType.Yoyo);
    }

    private void UpdateProgressSliderFillColor(Color value)
    {
        UpdateProgressSliderFill.color = value;
    }

    private void FinishDownload()
    {
        _pulseTween?.Kill();

        UpdatePercentage(100);
        UpdateProgressSliderFillColor(_completeColor);
    }
    
    private void Reset()
    {
        UpdateProgressSliderFillColor(_progressColor);
        UpdatePercentage(0);
    }
    
    private void UpdatePercentage(float percentage)
    {
        UpdateProgressPercentage.text = percentage.ToString("F1") + "%";
    }
}
