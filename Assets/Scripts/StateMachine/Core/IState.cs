using System.Collections.Generic;
using UnityEngine;

namespace StateMachine.Core
{
    /// <summary>
    /// Interface for all states in the finite state machine system.
    /// </summary>
    public interface IState
    {
        /// <summary>
        /// Unique identifier for this state.
        /// </summary>
        string StateId { get; }
        
        /// <summary>
        /// Called when entering this state.
        /// </summary>
        /// <param name="context">The state machine context</param>
        /// <param name="data">Optional data passed during transition</param>
        void OnEnter(IStateMachineContext context, object data = null);
        
        /// <summary>
        /// Called every frame while in this state.
        /// </summary>
        /// <param name="context">The state machine context</param>
        void OnUpdate(IStateMachineContext context);
        
        /// <summary>
        /// Called at fixed intervals while in this state.
        /// </summary>
        /// <param name="context">The state machine context</param>
        void OnFixedUpdate(IStateMachineContext context);
        
        /// <summary>
        /// Called when exiting this state.
        /// </summary>
        /// <param name="context">The state machine context</param>
        /// <param name="nextState">The state being transitioned to</param>
        void OnExit(IStateMachineContext context, IState nextState);
        
        /// <summary>
        /// Checks if this state can transition to another state.
        /// </summary>
        /// <param name="targetStateId">The target state ID</param>
        /// <param name="context">The state machine context</param>
        /// <returns>True if transition is allowed</returns>
        bool CanTransitionTo(string targetStateId, IStateMachineContext context);
    }
    
    /// <summary>
    /// Interface for state machine context that provides access to the owner and data.
    /// </summary>
    public interface IStateMachineContext
    {
        /// <summary>
        /// The GameObject that owns this state machine.
        /// </summary>
        GameObject Owner { get; }

        /// <summary>
        /// The MonoBehaviour component that manages this state machine.
        /// </summary>
        MonoBehaviour OwnerBehaviour { get; }

        /// <summary>
        /// Shared data dictionary for states to communicate.
        /// </summary>
        Dictionary<string, object> SharedData { get; }

        /// <summary>
        /// Time since the current state was entered.
        /// </summary>
        float TimeInCurrentState { get; }

        /// <summary>
        /// The current state of the state machine.
        /// </summary>
        IState CurrentState { get; }

        /// <summary>
        /// The previous state of the state machine.
        /// </summary>
        IState PreviousState { get; }

        /// <summary>
        /// Gets shared data of a specific type.
        /// </summary>
        /// <typeparam name="T">The data type</typeparam>
        /// <param name="key">The data key</param>
        /// <returns>The data, or default value if not found</returns>
        T GetSharedData<T>(string key);

        /// <summary>
        /// Sets shared data.
        /// </summary>
        /// <param name="key">The data key</param>
        /// <param name="value">The data value</param>
        void SetSharedData(string key, object value);

        /// <summary>
        /// Removes shared data.
        /// </summary>
        /// <param name="key">The data key</param>
        /// <returns>True if the data was removed</returns>
        bool RemoveSharedData(string key);

        /// <summary>
        /// Checks if shared data exists.
        /// </summary>
        /// <param name="key">The data key</param>
        /// <returns>True if the data exists</returns>
        bool HasSharedData(string key);
    }
}
