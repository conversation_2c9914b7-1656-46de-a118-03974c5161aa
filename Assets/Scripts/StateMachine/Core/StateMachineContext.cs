using System.Collections.Generic;
using UnityEngine;

namespace StateMachine.Core
{
    /// <summary>
    /// Implementation of IStateMachineContext that provides context information to states.
    /// </summary>
    public class StateMachineContext : IStateMachineContext
    {
        private readonly GameObject _owner;
        private readonly MonoBehaviour _ownerBehaviour;
        private readonly Dictionary<string, object> _sharedData;
        private readonly IStateMachine _stateMachine;
        
        /// <summary>
        /// The GameObject that owns this state machine.
        /// </summary>
        public GameObject Owner => _owner;
        
        /// <summary>
        /// The MonoBehaviour component that manages this state machine.
        /// </summary>
        public MonoBehaviour OwnerBehaviour => _ownerBehaviour;
        
        /// <summary>
        /// Shared data dictionary for states to communicate.
        /// </summary>
        public Dictionary<string, object> SharedData => _sharedData;
        
        /// <summary>
        /// Time since the current state was entered.
        /// </summary>
        public float TimeInCurrentState => _stateMachine?.TimeInCurrentState ?? 0f;
        
        /// <summary>
        /// The current state of the state machine.
        /// </summary>
        public IState CurrentState => _stateMachine?.CurrentState;
        
        /// <summary>
        /// The previous state of the state machine.
        /// </summary>
        public IState PreviousState => _stateMachine?.PreviousState;
        
        /// <summary>
        /// Constructor for StateMachineContext.
        /// </summary>
        /// <param name="owner">The GameObject that owns the state machine</param>
        /// <param name="ownerBehaviour">The MonoBehaviour that manages the state machine</param>
        /// <param name="stateMachine">The state machine instance</param>
        public StateMachineContext(GameObject owner, MonoBehaviour ownerBehaviour, IStateMachine stateMachine)
        {
            _owner = owner;
            _ownerBehaviour = ownerBehaviour;
            _stateMachine = stateMachine;
            _sharedData = new Dictionary<string, object>();
        }
        
        /// <summary>
        /// Gets a component from the owner GameObject.
        /// </summary>
        /// <typeparam name="T">The component type</typeparam>
        /// <returns>The component, or null if not found</returns>
        public T GetOwnerComponent<T>() where T : Component
        {
            return _owner.GetComponent<T>();
        }
        
        /// <summary>
        /// Gets a component from the owner GameObject or its children.
        /// </summary>
        /// <typeparam name="T">The component type</typeparam>
        /// <returns>The component, or null if not found</returns>
        public T GetOwnerComponentInChildren<T>() where T : Component
        {
            return _owner.GetComponentInChildren<T>();
        }
        
        /// <summary>
        /// Gets a component from the owner GameObject or its parents.
        /// </summary>
        /// <typeparam name="T">The component type</typeparam>
        /// <returns>The component, or null if not found</returns>
        public T GetOwnerComponentInParent<T>() where T : Component
        {
            return _owner.GetComponentInParent<T>();
        }

        /// <summary>
        /// Gets shared data of a specific type.
        /// </summary>
        /// <typeparam name="T">The data type</typeparam>
        /// <param name="key">The data key</param>
        /// <returns>The data, or default value if not found</returns>
        public T GetSharedData<T>(string key)
        {
            if (_sharedData.TryGetValue(key, out object value) && value is T)
            {
                return (T)value;
            }
            return default(T);
        }

        /// <summary>
        /// Sets shared data.
        /// </summary>
        /// <param name="key">The data key</param>
        /// <param name="value">The data value</param>
        public void SetSharedData(string key, object value)
        {
            _sharedData[key] = value;
        }

        /// <summary>
        /// Removes shared data.
        /// </summary>
        /// <param name="key">The data key</param>
        /// <returns>True if the data was removed</returns>
        public bool RemoveSharedData(string key)
        {
            return _sharedData.Remove(key);
        }

        /// <summary>
        /// Checks if shared data exists.
        /// </summary>
        /// <param name="key">The data key</param>
        /// <returns>True if the data exists</returns>
        public bool HasSharedData(string key)
        {
            return _sharedData.ContainsKey(key);
        }

        /// <summary>
        /// Clears all shared data.
        /// </summary>
        public void ClearSharedData()
        {
            _sharedData.Clear();
        }
    }
}
