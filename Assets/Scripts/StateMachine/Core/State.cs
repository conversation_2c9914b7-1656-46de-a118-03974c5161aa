using UnityEngine;

namespace StateMachine.Core
{
    /// <summary>
    /// Base class for all states. Provides default implementations and common functionality.
    /// </summary>
    public abstract class State : IState
    {
        [SerializeField] protected string stateId;
        
        /// <summary>
        /// Unique identifier for this state.
        /// </summary>
        public virtual string StateId => stateId;
        
        /// <summary>
        /// Constructor that sets the state ID.
        /// </summary>
        /// <param name="id">The unique identifier for this state</param>
        protected State(string id)
        {
            stateId = id;
        }
        
        /// <summary>
        /// Called when entering this state. Override to implement custom enter logic.
        /// </summary>
        /// <param name="context">The state machine context</param>
        /// <param name="data">Optional data passed during transition</param>
        public virtual void OnEnter(IStateMachineContext context, object data = null)
        {
            // Default implementation - can be overridden
        }
        
        /// <summary>
        /// Called every frame while in this state. Override to implement custom update logic.
        /// </summary>
        /// <param name="context">The state machine context</param>
        public virtual void OnUpdate(IStateMachineContext context)
        {
            // Default implementation - can be overridden
        }
        
        /// <summary>
        /// Called at fixed intervals while in this state. Override to implement custom fixed update logic.
        /// </summary>
        /// <param name="context">The state machine context</param>
        public virtual void OnFixedUpdate(IStateMachineContext context)
        {
            // Default implementation - can be overridden
        }
        
        /// <summary>
        /// Called when exiting this state. Override to implement custom exit logic.
        /// </summary>
        /// <param name="context">The state machine context</param>
        /// <param name="nextState">The state being transitioned to</param>
        public virtual void OnExit(IStateMachineContext context, IState nextState)
        {
            // Default implementation - can be overridden
        }
        
        /// <summary>
        /// Checks if this state can transition to another state. Override to implement custom transition logic.
        /// </summary>
        /// <param name="targetStateId">The target state ID</param>
        /// <param name="context">The state machine context</param>
        /// <returns>True if transition is allowed (default: always true)</returns>
        public virtual bool CanTransitionTo(string targetStateId, IStateMachineContext context)
        {
            // Default implementation allows all transitions
            return true;
        }
        
        /// <summary>
        /// Helper method to get a component from the owner GameObject.
        /// </summary>
        /// <typeparam name="T">The component type</typeparam>
        /// <param name="context">The state machine context</param>
        /// <returns>The component, or null if not found</returns>
        protected T GetOwnerComponent<T>(IStateMachineContext context) where T : Component
        {
            return context.Owner.GetComponent<T>();
        }
        
        /// <summary>
        /// Helper method to get shared data of a specific type.
        /// </summary>
        /// <typeparam name="T">The data type</typeparam>
        /// <param name="context">The state machine context</param>
        /// <param name="key">The data key</param>
        /// <returns>The data, or default value if not found</returns>
        protected T GetSharedData<T>(IStateMachineContext context, string key)
        {
            if (context.SharedData.TryGetValue(key, out object value) && value is T)
            {
                return (T)value;
            }
            return default(T);
        }
        
        /// <summary>
        /// Helper method to set shared data.
        /// </summary>
        /// <param name="context">The state machine context</param>
        /// <param name="key">The data key</param>
        /// <param name="value">The data value</param>
        protected void SetSharedData(IStateMachineContext context, string key, object value)
        {
            context.SharedData[key] = value;
        }
        
        public override string ToString()
        {
            return $"State: {StateId}";
        }
    }
}
