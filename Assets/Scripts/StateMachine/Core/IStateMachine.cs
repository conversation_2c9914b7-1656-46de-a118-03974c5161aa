using System;
using System.Collections.Generic;

namespace StateMachine.Core
{
    /// <summary>
    /// Interface for finite state machines.
    /// </summary>
    public interface IStateMachine
    {
        /// <summary>
        /// Event fired when a state transition occurs.
        /// </summary>
        event Action<IState, IState> OnStateChanged;
        
        /// <summary>
        /// The current active state.
        /// </summary>
        IState CurrentState { get; }
        
        /// <summary>
        /// The previous state.
        /// </summary>
        IState PreviousState { get; }
        
        /// <summary>
        /// Whether the state machine is currently running.
        /// </summary>
        bool IsRunning { get; }
        
        /// <summary>
        /// Time spent in the current state.
        /// </summary>
        float TimeInCurrentState { get; }
        
        /// <summary>
        /// Adds a state to the state machine.
        /// </summary>
        /// <param name="state">The state to add</param>
        void AddState(IState state);
        
        /// <summary>
        /// Removes a state from the state machine.
        /// </summary>
        /// <param name="stateId">The ID of the state to remove</param>
        /// <returns>True if the state was removed</returns>
        bool RemoveState(string stateId);
        
        /// <summary>
        /// Gets a state by its ID.
        /// </summary>
        /// <param name="stateId">The state ID</param>
        /// <returns>The state, or null if not found</returns>
        IState GetState(string stateId);
        
        /// <summary>
        /// Gets all registered states.
        /// </summary>
        /// <returns>Collection of all states</returns>
        IReadOnlyCollection<IState> GetAllStates();
        
        /// <summary>
        /// Starts the state machine with the specified initial state.
        /// </summary>
        /// <param name="initialStateId">The ID of the initial state</param>
        /// <param name="data">Optional data to pass to the initial state</param>
        void Start(string initialStateId, object data = null);
        
        /// <summary>
        /// Stops the state machine.
        /// </summary>
        void Stop();
        
        /// <summary>
        /// Transitions to a new state.
        /// </summary>
        /// <param name="targetStateId">The ID of the target state</param>
        /// <param name="data">Optional data to pass to the new state</param>
        /// <returns>True if the transition was successful</returns>
        bool TransitionTo(string targetStateId, object data = null);
        
        /// <summary>
        /// Forces a transition without checking conditions.
        /// </summary>
        /// <param name="targetStateId">The ID of the target state</param>
        /// <param name="data">Optional data to pass to the new state</param>
        /// <returns>True if the transition was successful</returns>
        bool ForceTransitionTo(string targetStateId, object data = null);
        
        /// <summary>
        /// Updates the state machine (should be called every frame).
        /// </summary>
        void Update();
        
        /// <summary>
        /// Fixed update for the state machine (should be called at fixed intervals).
        /// </summary>
        void FixedUpdate();
    }
}
