using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace StateMachine.Core
{
    /// <summary>
    /// Core implementation of a finite state machine.
    /// </summary>
    public class StateMachine : IStateMachine
    {
        private readonly Dictionary<string, IState> _states = new Dictionary<string, IState>();
        private readonly IStateMachineContext _context;
        
        private IState _currentState;
        private IState _previousState;
        private bool _isRunning;
        private float _timeInCurrentState;
        
        /// <summary>
        /// Event fired when a state transition occurs.
        /// </summary>
        public event Action<IState, IState> OnStateChanged;
        
        /// <summary>
        /// The current active state.
        /// </summary>
        public IState CurrentState => _currentState;
        
        /// <summary>
        /// The previous state.
        /// </summary>
        public IState PreviousState => _previousState;
        
        /// <summary>
        /// Whether the state machine is currently running.
        /// </summary>
        public bool IsRunning => _isRunning;
        
        /// <summary>
        /// Time spent in the current state.
        /// </summary>
        public float TimeInCurrentState => _timeInCurrentState;
        
        /// <summary>
        /// Constructor that requires a context.
        /// </summary>
        /// <param name="context">The state machine context</param>
        public StateMachine(IStateMachineContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }
        
        /// <summary>
        /// Adds a state to the state machine.
        /// </summary>
        /// <param name="state">The state to add</param>
        public void AddState(IState state)
        {
            if (state == null)
            {
                Debug.LogError("Cannot add null state to state machine");
                return;
            }
            
            if (string.IsNullOrEmpty(state.StateId))
            {
                Debug.LogError("Cannot add state with null or empty StateId");
                return;
            }
            
            if (_states.ContainsKey(state.StateId))
            {
                Debug.LogWarning($"State with ID '{state.StateId}' already exists. Replacing existing state.");
            }
            
            _states[state.StateId] = state;
        }
        
        /// <summary>
        /// Removes a state from the state machine.
        /// </summary>
        /// <param name="stateId">The ID of the state to remove</param>
        /// <returns>True if the state was removed</returns>
        public bool RemoveState(string stateId)
        {
            if (string.IsNullOrEmpty(stateId))
                return false;
                
            if (_currentState?.StateId == stateId)
            {
                Debug.LogError($"Cannot remove current state '{stateId}'. Transition to another state first.");
                return false;
            }
            
            return _states.Remove(stateId);
        }
        
        /// <summary>
        /// Gets a state by its ID.
        /// </summary>
        /// <param name="stateId">The state ID</param>
        /// <returns>The state, or null if not found</returns>
        public IState GetState(string stateId)
        {
            _states.TryGetValue(stateId, out IState state);
            return state;
        }
        
        /// <summary>
        /// Gets all registered states.
        /// </summary>
        /// <returns>Collection of all states</returns>
        public IReadOnlyCollection<IState> GetAllStates()
        {
            return _states.Values.ToList().AsReadOnly();
        }
        
        /// <summary>
        /// Starts the state machine with the specified initial state.
        /// </summary>
        /// <param name="initialStateId">The ID of the initial state</param>
        /// <param name="data">Optional data to pass to the initial state</param>
        public void Start(string initialStateId, object data = null)
        {
            if (_isRunning)
            {
                Debug.LogWarning("State machine is already running. Stop it first before starting again.");
                return;
            }
            
            var initialState = GetState(initialStateId);
            if (initialState == null)
            {
                Debug.LogError($"Cannot start state machine: Initial state '{initialStateId}' not found.");
                return;
            }
            
            _isRunning = true;
            _timeInCurrentState = 0f;
            _currentState = initialState;
            _previousState = null;
            
            _currentState.OnEnter(_context, data);
            OnStateChanged?.Invoke(null, _currentState);
        }
        
        /// <summary>
        /// Stops the state machine.
        /// </summary>
        public void Stop()
        {
            if (!_isRunning)
                return;
                
            if (_currentState != null)
            {
                _currentState.OnExit(_context, null);
            }
            
            _isRunning = false;
            _previousState = _currentState;
            _currentState = null;
            _timeInCurrentState = 0f;
        }
        
        /// <summary>
        /// Transitions to a new state.
        /// </summary>
        /// <param name="targetStateId">The ID of the target state</param>
        /// <param name="data">Optional data to pass to the new state</param>
        /// <returns>True if the transition was successful</returns>
        public bool TransitionTo(string targetStateId, object data = null)
        {
            return PerformTransition(targetStateId, data, false);
        }
        
        /// <summary>
        /// Forces a transition without checking conditions.
        /// </summary>
        /// <param name="targetStateId">The ID of the target state</param>
        /// <param name="data">Optional data to pass to the new state</param>
        /// <returns>True if the transition was successful</returns>
        public bool ForceTransitionTo(string targetStateId, object data = null)
        {
            return PerformTransition(targetStateId, data, true);
        }
        
        private bool PerformTransition(string targetStateId, object data, bool force)
        {
            if (!_isRunning)
            {
                Debug.LogError("Cannot transition: State machine is not running.");
                return false;
            }
            
            if (string.IsNullOrEmpty(targetStateId))
            {
                Debug.LogError("Cannot transition to null or empty state ID.");
                return false;
            }
            
            var targetState = GetState(targetStateId);
            if (targetState == null)
            {
                Debug.LogError($"Cannot transition: Target state '{targetStateId}' not found.");
                return false;
            }
            
            if (_currentState?.StateId == targetStateId)
            {
                Debug.LogWarning($"Already in state '{targetStateId}'. Ignoring transition.");
                return false;
            }
            
            // Check transition conditions unless forced
            if (!force && _currentState != null && !_currentState.CanTransitionTo(targetStateId, _context))
            {
                Debug.LogWarning($"Transition from '{_currentState.StateId}' to '{targetStateId}' is not allowed.");
                return false;
            }
            
            // Perform the transition
            var oldState = _currentState;
            _previousState = _currentState;
            _currentState = targetState;
            _timeInCurrentState = 0f;
            
            // Exit old state and enter new state
            oldState?.OnExit(_context, _currentState);
            _currentState.OnEnter(_context, data);
            
            OnStateChanged?.Invoke(oldState, _currentState);
            return true;
        }
        
        /// <summary>
        /// Updates the state machine (should be called every frame).
        /// </summary>
        public void Update()
        {
            if (!_isRunning || _currentState == null)
                return;
                
            _timeInCurrentState += Time.deltaTime;
            _currentState.OnUpdate(_context);
        }
        
        /// <summary>
        /// Fixed update for the state machine (should be called at fixed intervals).
        /// </summary>
        public void FixedUpdate()
        {
            if (!_isRunning || _currentState == null)
                return;
                
            _currentState.OnFixedUpdate(_context);
        }
    }
}
