using UnityEngine;
using StateMachine.Core;

namespace StateMachine.Conditions
{
    /// <summary>
    /// Transition condition based on time spent in current state.
    /// </summary>
    [System.Serializable]
    public class TimeCondition : ITransitionCondition
    {
        [SerializeField] private float duration = 1f;
        [SerializeField] private bool useRandomRange = false;
        [SerializeField] private float minDuration = 0.5f;
        [SerializeField] private float maxDuration = 2f;
        
        private float _targetDuration;
        private bool _initialized = false;
        
        /// <summary>
        /// Constructor for TimeCondition.
        /// </summary>
        /// <param name="duration">The duration to wait</param>
        public TimeCondition(float duration)
        {
            this.duration = duration;
        }
        
        /// <summary>
        /// Constructor for TimeCondition with random range.
        /// </summary>
        /// <param name="minDuration">Minimum duration</param>
        /// <param name="maxDuration">Maximum duration</param>
        public TimeCondition(float minDuration, float maxDuration)
        {
            this.useRandomRange = true;
            this.minDuration = minDuration;
            this.maxDuration = maxDuration;
        }
        
        /// <summary>
        /// Evaluates whether the time condition is met.
        /// </summary>
        /// <param name="context">The state machine context</param>
        /// <returns>True if enough time has passed</returns>
        public bool Evaluate(IStateMachineContext context)
        {
            if (!_initialized)
            {
                _targetDuration = useRandomRange ? 
                    Random.Range(minDuration, maxDuration) : 
                    duration;
                _initialized = true;
            }
            
            return context.TimeInCurrentState >= _targetDuration;
        }
        
        /// <summary>
        /// Resets the condition for reuse.
        /// </summary>
        public void Reset()
        {
            _initialized = false;
        }
    }
}
