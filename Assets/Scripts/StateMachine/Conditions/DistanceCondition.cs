using UnityEngine;
using StateMachine.Core;

namespace StateMachine.Conditions
{
    /// <summary>
    /// Transition condition based on distance to a target.
    /// </summary>
    [System.Serializable]
    public class DistanceCondition : ITransitionCondition
    {
        [SerializeField] private Transform target;
        [SerializeField] private float distance = 5f;
        [SerializeField] private ComparisonType comparison = ComparisonType.LessThan;
        [SerializeField] private string targetTag = "";
        [SerializeField] private bool findTargetByTag = false;
        
        public enum ComparisonType
        {
            LessThan,
            GreaterThan,
            Equal
        }
        
        /// <summary>
        /// Constructor for DistanceCondition.
        /// </summary>
        /// <param name="target">The target transform</param>
        /// <param name="distance">The distance threshold</param>
        /// <param name="comparison">The comparison type</param>
        public DistanceCondition(Transform target, float distance, ComparisonType comparison = ComparisonType.LessThan)
        {
            this.target = target;
            this.distance = distance;
            this.comparison = comparison;
        }
        
        /// <summary>
        /// Constructor for DistanceCondition with tag-based target finding.
        /// </summary>
        /// <param name="targetTag">The tag to search for</param>
        /// <param name="distance">The distance threshold</param>
        /// <param name="comparison">The comparison type</param>
        public DistanceCondition(string targetTag, float distance, ComparisonType comparison = ComparisonType.LessThan)
        {
            this.targetTag = targetTag;
            this.distance = distance;
            this.comparison = comparison;
            this.findTargetByTag = true;
        }
        
        /// <summary>
        /// Evaluates whether the distance condition is met.
        /// </summary>
        /// <param name="context">The state machine context</param>
        /// <returns>True if the distance condition is satisfied</returns>
        public bool Evaluate(IStateMachineContext context)
        {
            Transform targetTransform = GetTarget();
            
            if (targetTransform == null)
                return false;
                
            float currentDistance = Vector3.Distance(context.Owner.transform.position, targetTransform.position);
            
            return comparison switch
            {
                ComparisonType.LessThan => currentDistance < distance,
                ComparisonType.GreaterThan => currentDistance > distance,
                ComparisonType.Equal => Mathf.Approximately(currentDistance, distance),
                _ => false
            };
        }
        
        /// <summary>
        /// Gets the target transform, finding by tag if necessary.
        /// </summary>
        /// <returns>The target transform</returns>
        private Transform GetTarget()
        {
            if (target != null)
                return target;
                
            if (findTargetByTag && !string.IsNullOrEmpty(targetTag))
            {
                var targetObject = GameObject.FindGameObjectWithTag(targetTag);
                if (targetObject != null)
                {
                    target = targetObject.transform;
                    return target;
                }
            }
            
            return null;
        }
        
        /// <summary>
        /// Sets a new target.
        /// </summary>
        /// <param name="newTarget">The new target transform</param>
        public void SetTarget(Transform newTarget)
        {
            target = newTarget;
            findTargetByTag = false;
        }
        
        /// <summary>
        /// Sets the distance threshold.
        /// </summary>
        /// <param name="newDistance">The new distance threshold</param>
        public void SetDistance(float newDistance)
        {
            distance = newDistance;
        }
    }
}
