using StateMachine.Core;

namespace StateMachine.Conditions
{
    /// <summary>
    /// Interface for transition conditions that determine when state transitions can occur.
    /// </summary>
    public interface ITransitionCondition
    {
        /// <summary>
        /// Evaluates whether the transition condition is met.
        /// </summary>
        /// <param name="context">The state machine context</param>
        /// <returns>True if the condition is met</returns>
        bool Evaluate(IStateMachineContext context);
    }
}
