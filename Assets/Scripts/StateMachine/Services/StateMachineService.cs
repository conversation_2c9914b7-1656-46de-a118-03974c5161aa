using System;
using System.Collections.Generic;
using System.Linq;
using Services;
using UnityEngine;
using StateMachine.Core;
using StateMachineBehaviour = StateMachine.Unity.StateMachineBehaviour;

namespace StateMachine.Services
{
    /// <summary>
    /// Centralized service for managing all state machines in the game.
    /// Provides registration, lookup, debugging, and global control functionality.
    /// </summary>
    public class StateMachineService : MonoBehaviour
    {
        [Header("Service Configuration")]
        [SerializeField] private bool enableDebugLogging = true;
        [SerializeField] private bool enableGlobalPause = true;
        [SerializeField] private bool autoRegisterOnAwake = true;
        
        [Header("Debug Information")]
        [SerializeField] private int registeredStateMachinesCount = 0;
        [SerializeField] private int activeStateMachinesCount = 0;
        
        // Events
        public static event Action<string, IStateMachine> OnStateMachineRegistered;
        public static event Action<string, IStateMachine> OnStateMachineUnregistered;
        public static event Action<string, IState, IState> OnAnyStateChanged;
        public static event Action<bool> OnGlobalPauseChanged;
        
        // Private fields
        private readonly Dictionary<string, StateMachineRegistration> _registeredStateMachines = new Dictionary<string, StateMachineRegistration>();
        private readonly Dictionary<string, List<string>> _stateMachinesByTag = new Dictionary<string, List<string>>();
        private bool _isGloballyPaused = false;
        
        /// <summary>
        /// Registration information for a state machine.
        /// </summary>
        private class StateMachineRegistration
        {
            public string Id { get; set; }
            public IStateMachine StateMachine { get; set; }
            public StateMachineBehaviour Behaviour { get; set; }
            public GameObject Owner { get; set; }
            public string[] Tags { get; set; }
            public bool IsActive { get; set; }
            public DateTime RegisteredTime { get; set; }
        }
        
        /// <summary>
        /// Information about a registered state machine.
        /// </summary>
        public class StateMachineInfo
        {
            public string Id { get; internal set; }
            public IStateMachine StateMachine { get; internal set; }
            public StateMachineBehaviour Behaviour { get; internal set; }
            public GameObject Owner { get; internal set; }
            public string[] Tags { get; internal set; }
            public bool IsActive { get; internal set; }
            public DateTime RegisteredTime { get; internal set; }
            public IState CurrentState { get; internal set; }
            public float TimeInCurrentState { get; internal set; }
        }
        
        private void Awake()
        {
            ServiceLocator.Register(this);
        }
        
        private void Update()
        {
            UpdateDebugInformation();
        }
        
        /// <summary>
        /// Registers a state machine with the service.
        /// </summary>
        /// <param name="id">Unique identifier for the state machine</param>
        /// <param name="stateMachine">The state machine instance</param>
        /// <param name="behaviour">The StateMachineBehaviour component</param>
        /// <param name="tags">Optional tags for categorization</param>
        /// <returns>True if registration was successful</returns>
        public bool RegisterStateMachine(string id, IStateMachine stateMachine, StateMachineBehaviour behaviour, params string[] tags)
        {
            if (string.IsNullOrEmpty(id))
            {
                Debug.LogError("Cannot register state machine with null or empty ID");
                return false;
            }
            
            if (stateMachine == null)
            {
                Debug.LogError($"Cannot register null state machine with ID '{id}'");
                return false;
            }
            
            if (_registeredStateMachines.ContainsKey(id))
            {
                Debug.LogWarning($"State machine with ID '{id}' is already registered. Updating registration.");
                UnregisterStateMachine(id);
            }
            
            var registration = new StateMachineRegistration
            {
                Id = id,
                StateMachine = stateMachine,
                Behaviour = behaviour,
                Owner = behaviour?.gameObject,
                Tags = tags ?? new string[0],
                IsActive = stateMachine.IsRunning,
                RegisteredTime = DateTime.Now
            };
            
            _registeredStateMachines[id] = registration;
            
            // Add to tag collections
            foreach (string tag in registration.Tags)
            {
                if (!_stateMachinesByTag.ContainsKey(tag))
                {
                    _stateMachinesByTag[tag] = new List<string>();
                }
                _stateMachinesByTag[tag].Add(id);
            }
            
            // Subscribe to state change events
            stateMachine.OnStateChanged += (oldState, newState) => HandleStateChanged(id, oldState, newState);
            
            if (enableDebugLogging)
            {
                Debug.Log($"Registered state machine '{id}' with tags: [{string.Join(", ", tags)}]");
            }
            
            OnStateMachineRegistered?.Invoke(id, stateMachine);
            return true;
        }
        
        /// <summary>
        /// Unregisters a state machine from the service.
        /// </summary>
        /// <param name="id">The ID of the state machine to unregister</param>
        /// <returns>True if unregistration was successful</returns>
        public bool UnregisterStateMachine(string id)
        {
            if (!_registeredStateMachines.TryGetValue(id, out StateMachineRegistration registration))
            {
                return false;
            }
            
            // Remove from tag collections
            foreach (string tag in registration.Tags)
            {
                if (_stateMachinesByTag.TryGetValue(tag, out List<string> taggedIds))
                {
                    taggedIds.Remove(id);
                    if (taggedIds.Count == 0)
                    {
                        _stateMachinesByTag.Remove(tag);
                    }
                }
            }
            
            _registeredStateMachines.Remove(id);
            
            if (enableDebugLogging)
            {
                Debug.Log($"Unregistered state machine '{id}'");
            }
            
            OnStateMachineUnregistered?.Invoke(id, registration.StateMachine);
            return true;
        }
        
        /// <summary>
        /// Gets a registered state machine by ID.
        /// </summary>
        /// <param name="id">The state machine ID</param>
        /// <returns>The state machine, or null if not found</returns>
        public IStateMachine GetStateMachine(string id)
        {
            return _registeredStateMachines.TryGetValue(id, out StateMachineRegistration registration) 
                ? registration.StateMachine 
                : null;
        }
        
        /// <summary>
        /// Gets a registered StateMachineBehaviour by ID.
        /// </summary>
        /// <param name="id">The state machine ID</param>
        /// <returns>The StateMachineBehaviour, or null if not found</returns>
        public StateMachineBehaviour GetStateMachineBehaviour(string id)
        {
            return _registeredStateMachines.TryGetValue(id, out StateMachineRegistration registration) 
                ? registration.Behaviour 
                : null;
        }
        
        /// <summary>
        /// Gets all registered state machines with the specified tag.
        /// </summary>
        /// <param name="tag">The tag to search for</param>
        /// <returns>List of state machines with the tag</returns>
        public List<IStateMachine> GetStateMachinesByTag(string tag)
        {
            var result = new List<IStateMachine>();
            
            if (_stateMachinesByTag.TryGetValue(tag, out List<string> taggedIds))
            {
                foreach (string id in taggedIds)
                {
                    if (_registeredStateMachines.TryGetValue(id, out StateMachineRegistration registration))
                    {
                        result.Add(registration.StateMachine);
                    }
                }
            }
            
            return result;
        }
        
        /// <summary>
        /// Gets information about all registered state machines.
        /// </summary>
        /// <returns>List of state machine information</returns>
        public List<StateMachineInfo> GetAllStateMachineInfo()
        {
            return _registeredStateMachines.Values.Select(reg => new StateMachineInfo
            {
                Id = reg.Id,
                StateMachine = reg.StateMachine,
                Behaviour = reg.Behaviour,
                Owner = reg.Owner,
                Tags = reg.Tags,
                IsActive = reg.IsActive,
                RegisteredTime = reg.RegisteredTime,
                CurrentState = reg.StateMachine.CurrentState,
                TimeInCurrentState = reg.StateMachine.TimeInCurrentState
            }).ToList();
        }
        
        /// <summary>
        /// Starts all registered state machines with the specified tag.
        /// </summary>
        /// <param name="tag">The tag to filter by</param>
        /// <param name="initialStateId">Optional initial state ID</param>
        public void StartStateMachinesByTag(string tag, string initialStateId = null)
        {
            var stateMachines = GetStateMachinesByTag(tag);
            foreach (var stateMachine in stateMachines)
            {
                if (!stateMachine.IsRunning)
                {
                    if (!string.IsNullOrEmpty(initialStateId))
                    {
                        stateMachine.Start(initialStateId);
                    }
                    else
                    {
                        // Try to start with a default state if available
                        var states = stateMachine.GetAllStates();
                        if (states.Count > 0)
                        {
                            stateMachine.Start(states.First().StateId);
                        }
                    }
                }
            }
        }
        
        /// <summary>
        /// Stops all registered state machines with the specified tag.
        /// </summary>
        /// <param name="tag">The tag to filter by</param>
        public void StopStateMachinesByTag(string tag)
        {
            var stateMachines = GetStateMachinesByTag(tag);
            foreach (var stateMachine in stateMachines)
            {
                if (stateMachine.IsRunning)
                {
                    stateMachine.Stop();
                }
            }
        }
        
        /// <summary>
        /// Sets global pause state for all state machines.
        /// </summary>
        /// <param name="paused">Whether to pause or unpause</param>
        public void SetGlobalPause(bool paused)
        {
            if (!enableGlobalPause) return;
            
            _isGloballyPaused = paused;
            
            // Implementation would depend on how you want to handle pausing
            // For now, we'll just fire the event
            OnGlobalPauseChanged?.Invoke(paused);
            
            if (enableDebugLogging)
            {
                Debug.Log($"Global state machine pause: {paused}");
            }
        }
        
        /// <summary>
        /// Gets the current global pause state.
        /// </summary>
        /// <returns>True if globally paused</returns>
        public bool IsGloballyPaused()
        {
            return _isGloballyPaused;
        }
        
        /// <summary>
        /// Automatically registers all StateMachineBehaviour components in the scene.
        /// </summary>
        public void RegisterAllStateMachinesInScene()
        {
            var behaviours = FindObjectsOfType<StateMachineBehaviour>();
            
            foreach (var behaviour in behaviours)
            {
                string id = $"{behaviour.gameObject.name}_{behaviour.GetInstanceID()}";
                RegisterStateMachine(id, behaviour.StateMachine, behaviour, behaviour.gameObject.tag);
            }
            
            if (enableDebugLogging)
            {
                Debug.Log($"Auto-registered {behaviours.Length} state machines from scene");
            }
        }
        
        private void HandleStateChanged(string stateMachineId, IState oldState, IState newState)
        {
            OnAnyStateChanged?.Invoke(stateMachineId, oldState, newState);
            
            if (enableDebugLogging)
            {
                string oldStateName = oldState?.StateId ?? "None";
                string newStateName = newState?.StateId ?? "None";
                Debug.Log($"[{stateMachineId}] State changed: {oldStateName} -> {newStateName}");
            }
        }
        
        private void UpdateDebugInformation()
        {
            registeredStateMachinesCount = _registeredStateMachines.Count;
            activeStateMachinesCount = _registeredStateMachines.Values.Count(reg => reg.StateMachine.IsRunning);
            
            // Update active status
            foreach (var registration in _registeredStateMachines.Values)
            {
                registration.IsActive = registration.StateMachine.IsRunning;
            }
        }
    }
}
