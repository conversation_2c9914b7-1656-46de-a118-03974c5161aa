using System.Collections.Generic;
using System.Linq;
using Services;
using UnityEngine;
using StateMachine.Core;

namespace StateMachine.Services
{
    /// <summary>
    /// Debug component for visualizing and controlling state machines through the StateMachineService.
    /// </summary>
    public class StateMachineServiceDebugger : MonoBehaviour
    {
        [Header("Debug Settings")]
        [SerializeField] private bool showDebugGUI = true;
        [SerializeField] private bool showDetailedInfo = false;
        [SerializeField] private string filterTag = "";
        
        [Header("GUI Settings")]
        [SerializeField] private int guiWidth = 400;
        [SerializeField] private int guiHeight = 600;
        [SerializeField] private Vector2 guiPosition = new Vector2(10, 10);
        
        private Vector2 _scrollPosition;
        private bool _showRegisteredMachines = true;
        private bool _showGlobalControls = true;
        private string _selectedStateMachineId = "";
        
        private StateMachineService _stateMachineService;
        
        private StateMachineService StateMachineService
        {
            get
            {
                if (_stateMachineService == null)
                {
                    _stateMachineService = ServiceLocator.Locate<StateMachineService>();
                }
                
                return _stateMachineService;
            }
        }
        
        private void OnGUI()
        {
            if (!showDebugGUI || StateMachineService == null)
                return;
                
            GUILayout.BeginArea(new Rect(guiPosition.x, guiPosition.y, guiWidth, guiHeight), GUI.skin.box);
            
            GUILayout.Label("State Machine Service Debugger", GUI.skin.label);
            GUILayout.Space(10);
            
            DrawServiceInfo();
            DrawGlobalControls();
            DrawStateMachinesList();
            
            GUILayout.EndArea();
        }
        
        private void DrawServiceInfo()
        {
            var allInfo = StateMachineService.GetAllStateMachineInfo();
            int totalCount = allInfo.Count;
            int activeCount = allInfo.Count(info => info.IsActive);
            
            GUILayout.Label($"Total State Machines: {totalCount}");
            GUILayout.Label($"Active State Machines: {activeCount}");
            GUILayout.Label($"Global Pause: {StateMachineService.IsGloballyPaused()}");
            
            GUILayout.Space(5);
            
            // Filter controls
            GUILayout.BeginHorizontal();
            GUILayout.Label("Filter Tag:", GUILayout.Width(80));
            filterTag = GUILayout.TextField(filterTag, GUILayout.Width(100));
            if (GUILayout.Button("Clear", GUILayout.Width(50)))
            {
                filterTag = "";
            }
            GUILayout.EndHorizontal();
            
            GUILayout.Space(10);
        }
        
        private void DrawGlobalControls()
        {
            _showGlobalControls = GUILayout.Toggle(_showGlobalControls, "Global Controls");
            
            if (_showGlobalControls)
            {
                GUILayout.BeginVertical(GUI.skin.box);
                
                GUILayout.BeginHorizontal();
                if (GUILayout.Button("Pause All"))
                {
                    StateMachineService.SetGlobalPause(true);
                }
                if (GUILayout.Button("Resume All"))
                {
                    StateMachineService.SetGlobalPause(false);
                }
                GUILayout.EndHorizontal();
                
                if (!string.IsNullOrEmpty(filterTag))
                {
                    GUILayout.BeginHorizontal();
                    if (GUILayout.Button($"Start Tag: {filterTag}"))
                    {
                        StateMachineService.StartStateMachinesByTag(filterTag);
                    }
                    if (GUILayout.Button($"Stop Tag: {filterTag}"))
                    {
                        StateMachineService.StopStateMachinesByTag(filterTag);
                    }
                    GUILayout.EndHorizontal();
                }
                
                if (GUILayout.Button("Refresh Scene Registration"))
                {
                    StateMachineService.RegisterAllStateMachinesInScene();
                }
                
                GUILayout.EndVertical();
                GUILayout.Space(10);
            }
        }
        
        private void DrawStateMachinesList()
        {
            _showRegisteredMachines = GUILayout.Toggle(_showRegisteredMachines, "Registered State Machines");
            
            if (_showRegisteredMachines)
            {
                var allInfo = StateMachineService.GetAllStateMachineInfo();
                
                // Apply filter
                if (!string.IsNullOrEmpty(filterTag))
                {
                    allInfo = allInfo.Where(info => info.Tags.Contains(filterTag)).ToList();
                }
                
                _scrollPosition = GUILayout.BeginScrollView(_scrollPosition, GUILayout.Height(300));
                
                foreach (var info in allInfo)
                {
                    DrawStateMachineInfo(info);
                }
                
                GUILayout.EndScrollView();
            }
        }
        
        private void DrawStateMachineInfo(StateMachineService.StateMachineInfo info)
        {
            GUILayout.BeginVertical(GUI.skin.box);
            
            // Header with ID and status
            GUILayout.BeginHorizontal();
            
            Color originalColor = GUI.color;
            GUI.color = info.IsActive ? Color.green : Color.red;
            GUILayout.Label("●", GUILayout.Width(15));
            GUI.color = originalColor;
            
            bool isExpanded = _selectedStateMachineId == info.Id;
            if (GUILayout.Button(info.Id, GUI.skin.label))
            {
                _selectedStateMachineId = isExpanded ? "" : info.Id;
            }
            
            GUILayout.FlexibleSpace();
            
            // Control buttons
            if (info.IsActive)
            {
                if (GUILayout.Button("Stop", GUILayout.Width(50)))
                {
                    info.StateMachine.Stop();
                }
            }
            else
            {
                if (GUILayout.Button("Start", GUILayout.Width(50)))
                {
                    var states = info.StateMachine.GetAllStates();
                    if (states.Count > 0)
                    {
                        info.StateMachine.Start(states.First().StateId);
                    }
                }
            }
            
            GUILayout.EndHorizontal();
            
            // Basic info
            string currentStateName = info.CurrentState?.StateId ?? "None";
            GUILayout.Label($"Current State: {currentStateName}");
            
            if (info.IsActive)
            {
                GUILayout.Label($"Time in State: {info.TimeInCurrentState:F1}s");
            }
            
            // Detailed info when expanded
            if (isExpanded && showDetailedInfo)
            {
                GUILayout.Space(5);
                GUILayout.Label($"Owner: {info.Owner?.name ?? "None"}");
                GUILayout.Label($"Tags: [{string.Join(", ", info.Tags)}]");
                GUILayout.Label($"Registered: {info.RegisteredTime:HH:mm:ss}");
                
                // Available states
                var states = info.StateMachine.GetAllStates();
                if (states.Count > 0)
                {
                    GUILayout.Label("Available States:");
                    foreach (var state in states)
                    {
                        GUILayout.BeginHorizontal();
                        GUILayout.Space(20);
                        
                        bool isCurrent = state.StateId == currentStateName;
                        GUI.color = isCurrent ? Color.yellow : originalColor;
                        GUILayout.Label($"• {state.StateId}");
                        GUI.color = originalColor;
                        
                        if (!isCurrent && info.IsActive)
                        {
                            if (GUILayout.Button("→", GUILayout.Width(25)))
                            {
                                info.StateMachine.TransitionTo(state.StateId);
                            }
                        }
                        
                        GUILayout.EndHorizontal();
                    }
                }
            }
            
            GUILayout.EndVertical();
            GUILayout.Space(2);
        }
        
        /// <summary>
        /// Toggles the debug GUI visibility.
        /// </summary>
        public void ToggleDebugGUI()
        {
            showDebugGUI = !showDebugGUI;
        }
        
        /// <summary>
        /// Sets the filter tag for displaying state machines.
        /// </summary>
        /// <param name="tag">The tag to filter by</param>
        public void SetFilterTag(string tag)
        {
            filterTag = tag;
        }
        
        /// <summary>
        /// Focuses on a specific state machine in the debugger.
        /// </summary>
        /// <param name="stateMachineId">The ID of the state machine to focus on</param>
        public void FocusStateMachine(string stateMachineId)
        {
            _selectedStateMachineId = stateMachineId;
            showDebugGUI = true;
            _showRegisteredMachines = true;
        }
    }
}
