using UnityEngine;
using StateMachine.Core;
using StateMachine.Conditions;
using System.Collections.Generic;

namespace StateMachine.ScriptableStates
{
    /// <summary>
    /// ScriptableObject-based state that can be configured in the inspector.
    /// </summary>
    [CreateAssetMenu(fileName = "New Scriptable State", menuName = "State Machine/Scriptable State")]
    public class ScriptableState : ScriptableObject, IState
    {
        [Header("State Configuration")]
        [SerializeField] private string stateId;
        [SerializeField] private string displayName;
        [SerializeField, TextArea(3, 5)] private string description;
        
        [Header("Transition Settings")]
        [SerializeField] private List<TransitionRule> transitionRules = new List<TransitionRule>();
        [SerializeField] private bool allowAllTransitions = true;
        
        [Header("State Behavior")]
        [SerializeField] private List<StateAction> enterActions = new List<StateAction>();
        [SerializeField] private List<StateAction> updateActions = new List<StateAction>();
        [SerializeField] private List<StateAction> exitActions = new List<StateAction>();
        
        /// <summary>
        /// Unique identifier for this state.
        /// </summary>
        public string StateId => stateId;
        
        /// <summary>
        /// Display name for this state.
        /// </summary>
        public string DisplayName => displayName;
        
        /// <summary>
        /// Description of this state.
        /// </summary>
        public string Description => description;
        
        /// <summary>
        /// Transition rule configuration.
        /// </summary>
        [System.Serializable]
        public class TransitionRule
        {
            public string targetStateId;
            public bool isAllowed = true;
            public List<ITransitionCondition> conditions = new List<ITransitionCondition>();
        }
        
        /// <summary>
        /// State action configuration.
        /// </summary>
        [System.Serializable]
        public class StateAction
        {
            public string actionName;
            public bool isEnabled = true;
            // Additional action parameters can be added here
        }
        
        /// <summary>
        /// Called when entering this state.
        /// </summary>
        /// <param name="context">The state machine context</param>
        /// <param name="data">Optional data passed during transition</param>
        public virtual void OnEnter(IStateMachineContext context, object data = null)
        {
            if (Application.isPlaying)
            {
                Debug.Log($"Entering state: {DisplayName ?? StateId}");
            }
            
            // Execute enter actions
            foreach (var action in enterActions)
            {
                if (action.isEnabled)
                {
                    ExecuteAction(action, context, data);
                }
            }
        }
        
        /// <summary>
        /// Called every frame while in this state.
        /// </summary>
        /// <param name="context">The state machine context</param>
        public virtual void OnUpdate(IStateMachineContext context)
        {
            // Execute update actions
            foreach (var action in updateActions)
            {
                if (action.isEnabled)
                {
                    ExecuteAction(action, context);
                }
            }
            
            // Check for automatic transitions
            CheckAutomaticTransitions(context);
        }
        
        /// <summary>
        /// Called at fixed intervals while in this state.
        /// </summary>
        /// <param name="context">The state machine context</param>
        public virtual void OnFixedUpdate(IStateMachineContext context)
        {
            // Override in derived classes for fixed update logic
        }
        
        /// <summary>
        /// Called when exiting this state.
        /// </summary>
        /// <param name="context">The state machine context</param>
        /// <param name="nextState">The state being transitioned to</param>
        public virtual void OnExit(IStateMachineContext context, IState nextState)
        {
            if (Application.isPlaying)
            {
                Debug.Log($"Exiting state: {DisplayName ?? StateId}");
            }
            
            // Execute exit actions
            foreach (var action in exitActions)
            {
                if (action.isEnabled)
                {
                    ExecuteAction(action, context, nextState);
                }
            }
        }
        
        /// <summary>
        /// Checks if this state can transition to another state.
        /// </summary>
        /// <param name="targetStateId">The target state ID</param>
        /// <param name="context">The state machine context</param>
        /// <returns>True if transition is allowed</returns>
        public virtual bool CanTransitionTo(string targetStateId, IStateMachineContext context)
        {
            if (allowAllTransitions)
                return true;
                
            var rule = transitionRules.Find(r => r.targetStateId == targetStateId);
            if (rule == null)
                return false;
                
            if (!rule.isAllowed)
                return false;
                
            // Check all conditions
            foreach (var condition in rule.conditions)
            {
                if (!condition.Evaluate(context))
                    return false;
            }
            
            return true;
        }
        
        /// <summary>
        /// Executes a state action.
        /// </summary>
        /// <param name="action">The action to execute</param>
        /// <param name="context">The state machine context</param>
        /// <param name="data">Optional data</param>
        protected virtual void ExecuteAction(StateAction action, IStateMachineContext context, object data = null)
        {
            // Override in derived classes to implement custom actions
            // This is a placeholder for action execution
            Debug.Log($"Executing action: {action.actionName}");
        }
        
        /// <summary>
        /// Checks for automatic transitions based on conditions.
        /// </summary>
        /// <param name="context">The state machine context</param>
        protected virtual void CheckAutomaticTransitions(IStateMachineContext context)
        {
            foreach (var rule in transitionRules)
            {
                if (rule.isAllowed && rule.conditions.Count > 0)
                {
                    bool allConditionsMet = true;
                    foreach (var condition in rule.conditions)
                    {
                        if (!condition.Evaluate(context))
                        {
                            allConditionsMet = false;
                            break;
                        }
                    }
                    
                    if (allConditionsMet)
                    {
                        // Trigger transition through the state machine behaviour
                        var stateMachineBehaviour = context.OwnerBehaviour as StateMachine.Unity.StateMachineBehaviour;
                        stateMachineBehaviour?.TransitionTo(rule.targetStateId);
                        break; // Only trigger the first valid transition
                    }
                }
            }
        }
        
        /// <summary>
        /// Adds a transition rule.
        /// </summary>
        /// <param name="targetStateId">The target state ID</param>
        /// <param name="isAllowed">Whether the transition is allowed</param>
        public void AddTransitionRule(string targetStateId, bool isAllowed = true)
        {
            var existingRule = transitionRules.Find(r => r.targetStateId == targetStateId);
            if (existingRule == null)
            {
                transitionRules.Add(new TransitionRule
                {
                    targetStateId = targetStateId,
                    isAllowed = isAllowed
                });
            }
            else
            {
                existingRule.isAllowed = isAllowed;
            }
        }
        
        /// <summary>
        /// Removes a transition rule.
        /// </summary>
        /// <param name="targetStateId">The target state ID</param>
        public void RemoveTransitionRule(string targetStateId)
        {
            transitionRules.RemoveAll(r => r.targetStateId == targetStateId);
        }
        
        private void OnValidate()
        {
            // Ensure state ID is not empty
            if (string.IsNullOrEmpty(stateId))
            {
                stateId = name;
            }
            
            // Set display name if empty
            if (string.IsNullOrEmpty(displayName))
            {
                displayName = stateId;
            }
        }
    }
}
