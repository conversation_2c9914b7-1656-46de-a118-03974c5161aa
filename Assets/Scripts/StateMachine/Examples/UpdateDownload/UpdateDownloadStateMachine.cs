using UnityEngine;
using StateMachine.Core;
using StateMachine.Unity;
using System.Collections;
using StateMachineBehaviour = StateMachine.Unity.StateMachineBehaviour;

namespace StateMachine.Examples.UpdateDownload
{
    /// <summary>
    /// State machine controller for the comedic update download system.
    /// Manages the various frustrating stages of a fake game update.
    /// </summary>
    public class UpdateDownloadStateMachine : MonoBehaviour
    {
        [Header("Download Progress")]
        [SerializeField] private float currentProgress = 0f;
        [SerializeField] private float targetProgress = 100f;
        [SerializeField] private bool isDownloadActive = false;
        
        [Header("UI References")]
        [SerializeField] private UnityEngine.UI.Slider progressBar;
        [SerializeField] private UnityEngine.UI.Text progressText;
        [SerializeField] private UnityEngine.UI.Text statusText;
        [SerializeField] private UnityEngine.UI.Text speedText;
        [SerializeField] private GameObject errorDialog;
        [SerializeField] private GameObject blueScreenPanel;
        
        [Header("Download Settings")]
        [SerializeField] private float baseDownloadSpeed = 1f; // Progress per second
        [SerializeField] private float slowDownloadSpeed = 0.1f;
        [SerializeField] private float fastDownloadSpeed = 5f;
        
        // Events for other systems to listen to
        public System.Action<float> OnProgressChanged;
        public System.Action<string> OnStatusChanged;
        public System.Action OnDownloadCompleted;
        public System.Action OnDownloadFailed;
        
        private StateMachineBehaviour _stateMachine;
        private Coroutine _progressCoroutine;
        
        // Shared data keys
        public const string PROGRESS_KEY = "Progress";
        public const string DOWNLOAD_SPEED_KEY = "DownloadSpeed";
        public const string ERROR_COUNT_KEY = "ErrorCount";
        public const string RESTART_COUNT_KEY = "RestartCount";
        
        private void Awake()
        {
            _stateMachine = GetComponent<StateMachineBehaviour>();
            if (_stateMachine == null)
            {
                _stateMachine = gameObject.AddComponent<StateMachineBehaviour>();
            }
            
            // Subscribe to state machine events
            _stateMachine.OnStateChanged += HandleStateChanged;
            _stateMachine.OnStateMachineStarted += HandleStateMachineStarted;
        }
        
        private void Start()
        {
            InitializeUI();
            InitializeSharedData();
        }
        
        private void OnDestroy()
        {
            if (_stateMachine != null)
            {
                _stateMachine.OnStateChanged -= HandleStateChanged;
                _stateMachine.OnStateMachineStarted -= HandleStateMachineStarted;
            }
            
            if (_progressCoroutine != null)
            {
                StopCoroutine(_progressCoroutine);
            }
        }
        
        private void InitializeUI()
        {
            if (progressBar != null)
                progressBar.value = 0f;
                
            if (progressText != null)
                progressText.text = "0%";
                
            if (statusText != null)
                statusText.text = "Preparing download...";
                
            if (speedText != null)
                speedText.text = "0 KB/s";
                
            if (errorDialog != null)
                errorDialog.SetActive(false);
                
            if (blueScreenPanel != null)
                blueScreenPanel.SetActive(false);
        }
        
        private void InitializeSharedData()
        {
            var context = _stateMachine.GetContext();
            context.SetSharedData(PROGRESS_KEY, 0f);
            context.SetSharedData(DOWNLOAD_SPEED_KEY, baseDownloadSpeed);
            context.SetSharedData(ERROR_COUNT_KEY, 0);
            context.SetSharedData(RESTART_COUNT_KEY, 0);
        }
        
        private void HandleStateChanged(IState oldState, IState newState)
        {
            Debug.Log($"Update Download State: {oldState?.StateId ?? "None"} -> {newState?.StateId ?? "None"}");
        }
        
        private void HandleStateMachineStarted(string initialState)
        {
            Debug.Log($"Update Download Started with state: {initialState}");
        }
        
        /// <summary>
        /// Starts the update download process.
        /// </summary>
        public void StartDownload()
        {
            if (!_stateMachine.IsRunning)
            {
                _stateMachine.StartStateMachine("Initializing");
                isDownloadActive = true;
            }
        }
        
        /// <summary>
        /// Stops the update download process.
        /// </summary>
        public void StopDownload()
        {
            if (_stateMachine.IsRunning)
            {
                _stateMachine.StopStateMachine();
                isDownloadActive = false;
                
                if (_progressCoroutine != null)
                {
                    StopCoroutine(_progressCoroutine);
                    _progressCoroutine = null;
                }
            }
        }
        
        /// <summary>
        /// Updates the progress bar and related UI elements.
        /// </summary>
        /// <param name="progress">Progress value (0-100)</param>
        public void UpdateProgress(float progress)
        {
            currentProgress = Mathf.Clamp(progress, 0f, 100f);
            
            if (progressBar != null)
                progressBar.value = currentProgress / 100f;
                
            if (progressText != null)
                progressText.text = $"{currentProgress:F1}%";
                
            OnProgressChanged?.Invoke(currentProgress);
            
            // Update shared data
            var context = _stateMachine.GetContext();
            context.SetSharedData(PROGRESS_KEY, currentProgress);
        }
        
        /// <summary>
        /// Updates the status text.
        /// </summary>
        /// <param name="status">Status message</param>
        public void UpdateStatus(string status)
        {
            if (statusText != null)
                statusText.text = status;
                
            OnStatusChanged?.Invoke(status);
        }
        
        /// <summary>
        /// Updates the download speed display.
        /// </summary>
        /// <param name="speed">Speed in KB/s</param>
        public void UpdateSpeed(float speed)
        {
            if (speedText != null)
            {
                if (speed < 1000)
                    speedText.text = $"{speed:F1} KB/s";
                else
                    speedText.text = $"{speed / 1000f:F1} MB/s";
            }
        }
        
        /// <summary>
        /// Shows an error dialog.
        /// </summary>
        /// <param name="show">Whether to show or hide the dialog</param>
        public void ShowErrorDialog(bool show)
        {
            if (errorDialog != null)
                errorDialog.SetActive(show);
        }
        
        /// <summary>
        /// Shows the blue screen of death.
        /// </summary>
        /// <param name="show">Whether to show or hide the blue screen</param>
        public void ShowBlueScreen(bool show)
        {
            if (blueScreenPanel != null)
                blueScreenPanel.SetActive(show);
        }
        
        /// <summary>
        /// Starts a progress update coroutine.
        /// </summary>
        /// <param name="speed">Progress increase per second</param>
        /// <param name="targetProgress">Target progress to reach</param>
        public void StartProgressUpdate(float speed, float targetProgress = 100f)
        {
            if (_progressCoroutine != null)
            {
                StopCoroutine(_progressCoroutine);
            }
            
            _progressCoroutine = StartCoroutine(UpdateProgressCoroutine(speed, targetProgress));
        }
        
        /// <summary>
        /// Stops the progress update coroutine.
        /// </summary>
        public void StopProgressUpdate()
        {
            if (_progressCoroutine != null)
            {
                StopCoroutine(_progressCoroutine);
                _progressCoroutine = null;
            }
        }
        
        private IEnumerator UpdateProgressCoroutine(float speed, float targetProgress)
        {
            while (currentProgress < targetProgress && isDownloadActive)
            {
                currentProgress += speed * Time.deltaTime;
                UpdateProgress(currentProgress);
                
                // Simulate network fluctuations
                float randomSpeed = Random.Range(speed * 0.5f, speed * 1.5f);
                UpdateSpeed(randomSpeed * 100f); // Convert to KB/s for display
                
                yield return null;
            }
            
            _progressCoroutine = null;
        }
        
        /// <summary>
        /// Gets the current download progress.
        /// </summary>
        /// <returns>Progress value (0-100)</returns>
        public float GetProgress()
        {
            return currentProgress;
        }
        
        /// <summary>
        /// Sets the progress directly (for rollbacks and jumps).
        /// </summary>
        /// <param name="progress">New progress value (0-100)</param>
        public void SetProgress(float progress)
        {
            UpdateProgress(progress);
        }
        
        /// <summary>
        /// Increments the error count in shared data.
        /// </summary>
        public void IncrementErrorCount()
        {
            var context = _stateMachine.GetContext();
            int errorCount = context.GetSharedData<int>(ERROR_COUNT_KEY);
            context.SetSharedData(ERROR_COUNT_KEY, errorCount + 1);
        }
        
        /// <summary>
        /// Increments the restart count in shared data.
        /// </summary>
        public void IncrementRestartCount()
        {
            var context = _stateMachine.GetContext();
            int restartCount = context.GetSharedData<int>(RESTART_COUNT_KEY);
            context.SetSharedData(RESTART_COUNT_KEY, restartCount + 1);
        }
        
        /// <summary>
        /// Triggers download completion.
        /// </summary>
        public void CompleteDownload()
        {
            isDownloadActive = false;
            OnDownloadCompleted?.Invoke();
        }
        
        /// <summary>
        /// Triggers download failure.
        /// </summary>
        public void FailDownload()
        {
            isDownloadActive = false;
            OnDownloadFailed?.Invoke();
        }
    }
}
