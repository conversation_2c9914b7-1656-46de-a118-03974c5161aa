using UnityEngine;
using StateMachine.Core;

namespace StateMachine.Examples.UpdateDownload.States
{
    /// <summary>
    /// Restarting state that resets progress and starts over with dramatic messages.
    /// </summary>
    public class RestartingState : MonoBehaviour, IState
    {
        [Header("Restart Settings")]
        [SerializeField] private float restartDuration = 5f;
        [SerializeField] private string[] restartMessages = {
            "Restarting download from beginning...",
            "Clearing corrupted data...",
            "Resetting download manager...",
            "Reconnecting to update servers...",
            "Initializing fresh download session...",
            "Purging temporary files...",
            "Revalidating system requirements...",
            "Starting over with clean slate..."
        };
        
        private float _timer;
        private int _currentMessageIndex;
        private float _messageChangeInterval;
        private UpdateDownloadStateMachine _downloadManager;
        
        public string StateId => "Restarting";
        
        private void Awake()
        {
            _downloadManager = GetComponent<UpdateDownloadStateMachine>();
        }
        
        public void OnEnter(IStateMachineContext context, object data = null)
        {
            _timer = 0f;
            _currentMessageIndex = 0;
            _messageChangeInterval = restartDuration / restartMessages.Length;
            
            // Stop all progress
            _downloadManager.StopProgressUpdate();
            _downloadManager.UpdateSpeed(0f);
            
            // Reset progress to 0
            _downloadManager.SetProgress(0f);
            
            // Increment restart count
            _downloadManager.IncrementRestartCount();
            
            // Show first restart message
            if (restartMessages.Length > 0)
            {
                _downloadManager.UpdateStatus(restartMessages[0]);
            }
            
            // Reset error count since we're starting fresh
            var sharedData = context.SharedData;
            sharedData[UpdateDownloadStateMachine.ERROR_COUNT_KEY] = 0;
            
            Debug.Log("Download restarting from 0%...");
        }
        
        public void OnUpdate(IStateMachineContext context)
        {
            _timer += Time.deltaTime;
            
            // Change messages periodically
            int targetMessageIndex = Mathf.FloorToInt(_timer / _messageChangeInterval);
            if (targetMessageIndex != _currentMessageIndex && targetMessageIndex < restartMessages.Length)
            {
                _currentMessageIndex = targetMessageIndex;
                _downloadManager.UpdateStatus(restartMessages[_currentMessageIndex]);
            }
            
            // After restart duration, go back to initializing
            if (_timer >= restartDuration)
            {
                var stateMachine = context.OwnerBehaviour as StateMachine.Unity.StateMachineBehaviour;
                stateMachine?.TransitionTo("Initializing");
            }
        }
        
        public void OnFixedUpdate(IStateMachineContext context)
        {
            // No fixed update needed
        }
        
        public void OnExit(IStateMachineContext context, IState nextState)
        {
            Debug.Log("Restart complete, beginning fresh download attempt");
        }
        
        public bool CanTransitionTo(string targetStateId, IStateMachineContext context)
        {
            // Can only transition to initializing
            return targetStateId == "Initializing";
        }
        
        /// <summary>
        /// Gets the number of times the download has been restarted.
        /// </summary>
        /// <param name="context">State machine context</param>
        /// <returns>Restart count</returns>
        public int GetRestartCount(IStateMachineContext context)
        {
            return context.GetSharedData<int>(UpdateDownloadStateMachine.RESTART_COUNT_KEY);
        }
    }
}
