using UnityEngine;
using StateMachine.Core;

namespace StateMachine.Examples.UpdateDownload.States
{
    /// <summary>
    /// Slowdown state where download speed becomes painfully slow with various excuses.
    /// </summary>
    public class SlowdownState : MonoBehaviour, IState
    {
        [Header("Slowdown Settings")]
        [SerializeField] private float slowdownDuration = 8f;
        [SerializeField] private float crawlSpeed = 0.1f;
        [SerializeField] private float recoverySpeed = 1.5f;
        
        [Header("Slowdown Messages")]
        [SerializeField] private string[] slowdownMessages = {
            "Network congestion detected. Reducing speed...",
            "Server load is high. Download speed limited...",
            "Your ISP is throttling the connection...",
            "Peak hours detected. Optimizing bandwidth...",
            "Background updates are running. Please wait...",
            "Antivirus is scanning files. Speed reduced...",
            "Windows Update is downloading. Sharing bandwidth...",
            "Steam is updating other games. Prioritizing...",
            "Your internet plan has speed limits...",
            "Router overheating. Reducing network load..."
        };
        
        [Header("Recovery Messages")]
        [SerializeField] private string[] recoveryMessages = {
            "Network conditions improved. Increasing speed...",
            "Server load decreased. Resuming normal speed...",
            "Bandwidth optimization complete...",
            "Background processes finished. Accelerating...",
            "Connection stabilized. Full speed ahead!"
        };
        
        private float _timer;
        private bool _isRecovering;
        private float _recoveryStartTime;
        private UpdateDownloadStateMachine _downloadManager;
        
        public string StateId => "Slowdown";
        
        private void Awake()
        {
            _downloadManager = GetComponent<UpdateDownloadStateMachine>();
        }
        
        public void OnEnter(IStateMachineContext context, object data = null)
        {
            _timer = 0f;
            _isRecovering = false;
            
            // Choose random slowdown message
            string slowdownMessage = slowdownMessages[Random.Range(0, slowdownMessages.Length)];
            _downloadManager.UpdateStatus(slowdownMessage);
            
            // Start crawling at very slow speed
            _downloadManager.StopProgressUpdate();
            _downloadManager.StartProgressUpdate(crawlSpeed);
            
            Debug.Log($"Download slowed down: {slowdownMessage}");
        }
        
        public void OnUpdate(IStateMachineContext context)
        {
            _timer += Time.deltaTime;
            
            // After slowdown duration, start recovery
            if (!_isRecovering && _timer >= slowdownDuration)
            {
                StartRecovery();
            }
            
            // Check if recovery is complete
            if (_isRecovering && _timer >= slowdownDuration + 2f) // 2 seconds recovery time
            {
                var stateMachine = context.OwnerBehaviour as StateMachine.Unity.StateMachineBehaviour;
                stateMachine?.TransitionTo("Downloading");
            }
        }
        
        public void OnFixedUpdate(IStateMachineContext context)
        {
            // No fixed update needed
        }
        
        public void OnExit(IStateMachineContext context, IState nextState)
        {
            _downloadManager.StopProgressUpdate();
            Debug.Log("Slowdown period ended, returning to normal speed");
        }
        
        public bool CanTransitionTo(string targetStateId, IStateMachineContext context)
        {
            // Can transition back to downloading or to error states
            return targetStateId == "Downloading" || targetStateId == "Error";
        }
        
        private void StartRecovery()
        {
            _isRecovering = true;
            _recoveryStartTime = _timer;
            
            // Choose random recovery message
            string recoveryMessage = recoveryMessages[Random.Range(0, recoveryMessages.Length)];
            _downloadManager.UpdateStatus(recoveryMessage);
            
            // Gradually increase speed
            _downloadManager.StopProgressUpdate();
            _downloadManager.StartProgressUpdate(recoverySpeed);
            
            Debug.Log($"Starting recovery: {recoveryMessage}");
        }
        
        /// <summary>
        /// Manually trigger speed recovery (can be called by UI).
        /// </summary>
        public void ForceRecovery()
        {
            if (!_isRecovering)
            {
                StartRecovery();
            }
        }
        
        /// <summary>
        /// Gets the current slowdown factor.
        /// </summary>
        /// <returns>Speed multiplier (0-1)</returns>
        public float GetSlowdownFactor()
        {
            if (_isRecovering)
            {
                float recoveryProgress = (_timer - _recoveryStartTime) / 2f; // 2 second recovery
                return Mathf.Lerp(0.1f, 1f, recoveryProgress);
            }
            return 0.1f; // 10% of normal speed during slowdown
        }
    }
}
