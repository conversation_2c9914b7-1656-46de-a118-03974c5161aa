using UnityEngine;
using StateMachine.Core;

namespace StateMachine.Examples.UpdateDownload.States
{
    /// <summary>
    /// Installing state - the final step that can still have its own comedic issues.
    /// </summary>
    public class InstallingState : MonoBehaviour, IState
    {
        [Header("Installation Settings")]
        [SerializeField] private float installationTime = 6f;
        [SerializeField] private float installSpeed = 3f;
        [SerializeField] private float finalHangTime = 2f;
        
        [Header("Installation Messages")]
        [SerializeField] private string[] installMessages = {
            "Installing update...",
            "Extracting files...",
            "Updating game assets...",
            "Configuring settings...",
            "Optimizing performance...",
            "Finalizing installation...",
            "Almost done..."
        };
        
        [Header("Final Messages")]
        [SerializeField] private string[] finalMessages = {
            "Installation complete!",
            "Update successfully installed!",
            "Ready to play!",
            "Game is up to date!",
            "Installation finished!"
        };
        
        private float _timer;
        private int _currentMessageIndex;
        private float _messageChangeInterval;
        private bool _installationComplete;
        private bool _finalHangStarted;
        private UpdateDownloadStateMachine _downloadManager;
        
        public string StateId => "Installing";
        
        private void Awake()
        {
            _downloadManager = GetComponent<UpdateDownloadStateMachine>();
        }
        
        public void OnEnter(IStateMachineContext context, object data = null)
        {
            _timer = 0f;
            _currentMessageIndex = 0;
            _installationComplete = false;
            _finalHangStarted = false;
            _messageChangeInterval = installationTime / installMessages.Length;
            
            // Ensure we're at 100% progress
            _downloadManager.SetProgress(100f);
            _downloadManager.UpdateSpeed(0f);
            
            // Show first installation message
            if (installMessages.Length > 0)
            {
                _downloadManager.UpdateStatus(installMessages[0]);
            }
            
            Debug.Log("Starting installation process...");
        }
        
        public void OnUpdate(IStateMachineContext context)
        {
            _timer += Time.deltaTime;
            
            if (!_installationComplete)
            {
                // Change messages periodically during installation
                int targetMessageIndex = Mathf.FloorToInt(_timer / _messageChangeInterval);
                if (targetMessageIndex != _currentMessageIndex && targetMessageIndex < installMessages.Length)
                {
                    _currentMessageIndex = targetMessageIndex;
                    _downloadManager.UpdateStatus(installMessages[_currentMessageIndex]);
                }
                
                // Check if installation time is up
                if (_timer >= installationTime)
                {
                    CompleteInstallation();
                }
            }
            else if (!_finalHangStarted)
            {
                // Start the final hang period (simulating the "99% complete" hang)
                StartFinalHang();
            }
            else if (_timer >= installationTime + finalHangTime)
            {
                // Installation truly complete
                FinishInstallation(context);
            }
        }
        
        public void OnFixedUpdate(IStateMachineContext context)
        {
            // No fixed update needed
        }
        
        public void OnExit(IStateMachineContext context, IState nextState)
        {
            Debug.Log("Installation state complete");
        }
        
        public bool CanTransitionTo(string targetStateId, IStateMachineContext context)
        {
            // Can transition to completed state or potentially to error
            return targetStateId == "Completed" || targetStateId == "Error";
        }
        
        private void CompleteInstallation()
        {
            _installationComplete = true;
            
            // Small chance of installation error (5%)
            if (Random.Range(0f, 1f) < 0.05f)
            {
                _downloadManager.UpdateStatus("Installation failed. Retrying...");
                var stateMachine = GetComponent<StateMachine.Unity.StateMachineBehaviour>();
                stateMachine?.TransitionTo("Error");
                return;
            }
            
            // Show completion message
            string finalMessage = finalMessages[Random.Range(0, finalMessages.Length)];
            _downloadManager.UpdateStatus(finalMessage);
        }
        
        private void StartFinalHang()
        {
            _finalHangStarted = true;
            
            // Show the dreaded "Finalizing..." message that hangs forever
            _downloadManager.UpdateStatus("Finalizing... (99%)");
            
            Debug.Log("Starting final hang period - the most frustrating part!");
        }
        
        private void FinishInstallation(IStateMachineContext context)
        {
            // Show final success message
            _downloadManager.UpdateStatus("Installation complete! Game ready to play.");
            
            // Trigger completion
            _downloadManager.CompleteDownload();
            
            // Transition to completed state
            var stateMachine = context.OwnerBehaviour as StateMachine.Unity.StateMachineBehaviour;
            stateMachine?.TransitionTo("Completed");
        }
        
        /// <summary>
        /// Gets the installation progress (separate from download progress).
        /// </summary>
        /// <returns>Installation progress (0-100)</returns>
        public float GetInstallationProgress()
        {
            if (_installationComplete)
            {
                return _finalHangStarted ? 99f : 100f;
            }
            
            return (_timer / installationTime) * 100f;
        }
    }
}
