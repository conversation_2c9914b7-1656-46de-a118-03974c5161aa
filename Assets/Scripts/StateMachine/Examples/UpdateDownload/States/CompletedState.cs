using UnityEngine;
using StateMachine.Core;

namespace StateMachine.Examples.UpdateDownload.States
{
    /// <summary>
    /// Final completed state - the download and installation are finally done!
    /// </summary>
    public class CompletedState : MonoBehaviour, IState
    {
        [Header("Completion Settings")]
        [SerializeField] private string[] completionMessages = {
            "Update complete! Game is ready to play.",
            "Installation successful! Enjoy your game.",
            "All done! Your game is up to date.",
            "Update installed successfully!",
            "Ready to play! Update complete."
        };
        
        [Header("Statistics Display")]
        [SerializeField] private bool showStatistics = true;
        
        private UpdateDownloadStateMachine _downloadManager;
        
        public string StateId => "Completed";
        
        private void Awake()
        {
            _downloadManager = GetComponent<UpdateDownloadStateMachine>();
        }
        
        public void OnEnter(IStateMachineContext context, object data = null)
        {
            // Ensure progress is at 100%
            _downloadManager.SetProgress(100f);
            _downloadManager.UpdateSpeed(0f);
            
            // Show completion message
            string completionMessage = completionMessages[Random.Range(0, completionMessages.Length)];
            _downloadManager.UpdateStatus(completionMessage);
            
            // Display statistics if enabled
            if (showStatistics)
            {
                DisplayStatistics(context);
            }
            
            // Hide any error dialogs or blue screens
            _downloadManager.ShowErrorDialog(false);
            _downloadManager.ShowBlueScreen(false);
            
            Debug.Log("Update download and installation completed successfully!");
        }
        
        public void OnUpdate(IStateMachineContext context)
        {
            // This state doesn't need to do anything in update
            // The download is complete and the player can now play the game
        }
        
        public void OnFixedUpdate(IStateMachineContext context)
        {
            // No fixed update needed
        }
        
        public void OnExit(IStateMachineContext context, IState nextState)
        {
            Debug.Log("Exiting completed state");
        }
        
        public bool CanTransitionTo(string targetStateId, IStateMachineContext context)
        {
            // Completed state is typically final, but could potentially restart
            return targetStateId == "Initializing"; // Allow restart if needed
        }
        
        private void DisplayStatistics(IStateMachineContext context)
        {
            // Get statistics from shared data
            int errorCount = context.GetSharedData<int>(UpdateDownloadStateMachine.ERROR_COUNT_KEY);
            int restartCount = context.GetSharedData<int>(UpdateDownloadStateMachine.RESTART_COUNT_KEY);
            
            // Calculate some humorous statistics
            string statisticsMessage = GenerateStatisticsMessage(errorCount, restartCount);
            
            Debug.Log($"Download Statistics: {statisticsMessage}");
            
            // You could display this in UI if desired
            // For now, we'll just log it
        }
        
        private string GenerateStatisticsMessage(int errorCount, int restartCount)
        {
            string message = "Download Statistics:\n";
            
            if (errorCount == 0 && restartCount == 0)
            {
                message += "Miraculously completed without any issues!";
            }
            else
            {
                message += $"Errors encountered: {errorCount}\n";
                message += $"Restarts required: {restartCount}\n";
                
                // Add some humor based on the numbers
                if (errorCount > 5)
                {
                    message += "Your internet connection fought valiantly!";
                }
                else if (restartCount > 2)
                {
                    message += "Persistence pays off!";
                }
                else if (errorCount > 0 || restartCount > 0)
                {
                    message += "A typical download experience.";
                }
            }
            
            return message;
        }
        
        /// <summary>
        /// Restarts the entire download process (for testing or if player wants to see it again).
        /// </summary>
        public void RestartDownload()
        {
            var stateMachine = GetComponent<StateMachine.Unity.StateMachineBehaviour>();
            if (stateMachine != null)
            {
                // Reset all shared data
                var context = stateMachine.GetContext();
                context.SetSharedData(UpdateDownloadStateMachine.PROGRESS_KEY, 0f);
                context.SetSharedData(UpdateDownloadStateMachine.ERROR_COUNT_KEY, 0);
                context.SetSharedData(UpdateDownloadStateMachine.RESTART_COUNT_KEY, 0);
                
                // Transition back to initializing
                stateMachine.TransitionTo("Initializing");
            }
        }
        
        /// <summary>
        /// Checks if the download process is truly complete.
        /// </summary>
        /// <returns>True if download and installation are complete</returns>
        public bool IsComplete()
        {
            return true; // This state represents completion
        }
    }
}
