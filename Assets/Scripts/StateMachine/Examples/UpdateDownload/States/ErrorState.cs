using UnityEngine;
using StateMachine.Core;

namespace StateMachine.Examples.UpdateDownload.States
{
    /// <summary>
    /// Error state that shows various download errors and prompts for restart.
    /// </summary>
    public class ErrorState : MonoBehaviour, IState
    {
        [Header("Error Settings")]
        [SerializeField] private float errorDisplayTime = 4f;
        [SerializeField] private string[] errorMessages = {
            "Network connection lost. Retrying...",
            "Server timeout. Please try again.",
            "Insufficient disk space. Please free up space.",
            "Download corrupted. Restarting...",
            "Authentication failed. Reconnecting...",
            "Update server is busy. Please wait...",
            "Antivirus software blocked download.",
            "Windows Update is interfering. Please wait.",
            "Your internet is too slow. Optimizing...",
            "Download limit exceeded. Waiting for reset..."
        };
        
        private float _timer;
        private string _currentErrorMessage;
        private UpdateDownloadStateMachine _downloadManager;
        
        public string StateId => "Error";
        
        private void Awake()
        {
            _downloadManager = GetComponent<UpdateDownloadStateMachine>();
        }
        
        public void OnEnter(IStateMachineContext context, object data = null)
        {
            _timer = 0f;
            
            // Choose a random error message
            _currentErrorMessage = errorMessages[Random.Range(0, errorMessages.Length)];
            
            // Stop any ongoing progress
            _downloadManager.StopProgressUpdate();
            _downloadManager.UpdateStatus(_currentErrorMessage);
            _downloadManager.UpdateSpeed(0f);
            
            // Show error dialog
            _downloadManager.ShowErrorDialog(true);
            
            // Increment error count
            _downloadManager.IncrementErrorCount();
            
            Debug.Log($"Download error occurred: {_currentErrorMessage}");
        }
        
        public void OnUpdate(IStateMachineContext context)
        {
            _timer += Time.deltaTime;
            
            // After error display time, decide what to do next
            if (_timer >= errorDisplayTime)
            {
                DecideNextAction(context);
            }
        }
        
        public void OnFixedUpdate(IStateMachineContext context)
        {
            // No fixed update needed
        }
        
        public void OnExit(IStateMachineContext context, IState nextState)
        {
            // Hide error dialog
            _downloadManager.ShowErrorDialog(false);
            
            Debug.Log($"Resolving error, transitioning to {nextState?.StateId}");
        }
        
        public bool CanTransitionTo(string targetStateId, IStateMachineContext context)
        {
            // Can transition to downloading (retry) or restart
            return targetStateId == "Downloading" || targetStateId == "Restarting";
        }
        
        private void DecideNextAction(IStateMachineContext context)
        {
            var stateMachine = context.OwnerBehaviour as StateMachine.Unity.StateMachineBehaviour;
            if (stateMachine == null) return;
            
            // Get error count from shared data
            int errorCount = context.GetSharedData<int>(UpdateDownloadStateMachine.ERROR_COUNT_KEY);
            
            // If too many errors, force a restart
            if (errorCount >= 3)
            {
                _downloadManager.UpdateStatus("Too many errors. Restarting download...");
                stateMachine.TransitionTo("Restarting");
            }
            else
            {
                // Random chance to restart vs retry
                if (Random.Range(0f, 1f) < 0.3f) // 30% chance to restart
                {
                    _downloadManager.UpdateStatus("Restarting download...");
                    stateMachine.TransitionTo("Restarting");
                }
                else
                {
                    _downloadManager.UpdateStatus("Retrying download...");
                    stateMachine.TransitionTo("Downloading");
                }
            }
        }
        
        /// <summary>
        /// Manually trigger error resolution (can be called by UI button).
        /// </summary>
        public void ResolveError()
        {
            var context = GetComponent<StateMachine.Unity.StateMachineBehaviour>()?.GetContext();
            if (context != null)
            {
                DecideNextAction(context);
            }
        }
    }
}
