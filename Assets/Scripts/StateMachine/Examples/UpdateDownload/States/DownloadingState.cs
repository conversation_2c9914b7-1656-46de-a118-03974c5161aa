using UnityEngine;
using StateMachine.Core;

namespace StateMachine.Examples.UpdateDownload.States
{
    /// <summary>
    /// Main downloading state with various comedic interruptions and slowdowns.
    /// </summary>
    public class DownloadingState : MonoBehaviour, IState
    {
        [Header("Download Behavior")]
        [SerializeField] private float normalSpeed = 2f;
        [SerializeField] private float slowSpeed = 0.3f;
        [SerializeField] private float fastSpeed = 8f;
        
        [Header("Interruption Chances")]
        [SerializeField] private float errorChance = 0.15f;
        [SerializeField] private float slowdownChance = 0.25f;
        [SerializeField] private float rollbackChance = 0.1f;
        [SerializeField] private float blueScreenChance = 0.05f;
        
        [Header("Progress Thresholds")]
        [SerializeField] private float[] interruptionThresholds = { 25f, 50f, 75f, 90f, 99f };
        
        private float _currentSpeed;
        private float _checkTimer;
        private float _checkInterval = 2f;
        private bool[] _thresholdTriggered;
        private UpdateDownloadStateMachine _downloadManager;
        
        public string StateId => "Downloading";
        
        private void Awake()
        {
            _downloadManager = GetComponent<UpdateDownloadStateMachine>();
            _thresholdTriggered = new bool[interruptionThresholds.Length];
        }
        
        public void OnEnter(IStateMachineContext context, object data = null)
        {
            _currentSpeed = normalSpeed;
            _checkTimer = 0f;
            
            // Reset threshold triggers
            for (int i = 0; i < _thresholdTriggered.Length; i++)
            {
                _thresholdTriggered[i] = false;
            }
            
            _downloadManager.UpdateStatus("Downloading update...");
            _downloadManager.StartProgressUpdate(_currentSpeed);
            
            Debug.Log("Started downloading at normal speed");
        }
        
        public void OnUpdate(IStateMachineContext context)
        {
            _checkTimer += Time.deltaTime;
            
            // Periodically check for interruptions
            if (_checkTimer >= _checkInterval)
            {
                _checkTimer = 0f;
                CheckForInterruptions(context);
            }
            
            // Check if download is complete
            float currentProgress = _downloadManager.GetProgress();
            if (currentProgress >= 100f)
            {
                var stateMachine = context.OwnerBehaviour as StateMachine.Unity.StateMachineBehaviour;
                stateMachine?.TransitionTo("Installing");
            }
        }
        
        public void OnFixedUpdate(IStateMachineContext context)
        {
            // No fixed update needed
        }
        
        public void OnExit(IStateMachineContext context, IState nextState)
        {
            _downloadManager.StopProgressUpdate();
            Debug.Log($"Exiting download state to {nextState?.StateId}");
        }
        
        public bool CanTransitionTo(string targetStateId, IStateMachineContext context)
        {
            // Can transition to various interruption states or completion
            return targetStateId == "Installing" || 
                   targetStateId == "Error" || 
                   targetStateId == "Slowdown" || 
                   targetStateId == "Rollback" || 
                   targetStateId == "BlueScreen";
        }
        
        private void CheckForInterruptions(IStateMachineContext context)
        {
            float currentProgress = _downloadManager.GetProgress();
            var stateMachine = context.OwnerBehaviour as StateMachine.Unity.StateMachineBehaviour;
            
            // Check threshold-based interruptions
            for (int i = 0; i < interruptionThresholds.Length; i++)
            {
                if (!_thresholdTriggered[i] && currentProgress >= interruptionThresholds[i])
                {
                    _thresholdTriggered[i] = true;
                    
                    // Higher chance of interruption at certain thresholds
                    float interruptionMultiplier = GetInterruptionMultiplier(interruptionThresholds[i]);
                    
                    if (TriggerInterruption(interruptionMultiplier))
                    {
                        string interruptionType = ChooseInterruption(interruptionMultiplier);
                        stateMachine?.TransitionTo(interruptionType);
                        return;
                    }
                }
            }
            
            // Random interruptions during normal download
            if (Random.Range(0f, 1f) < 0.1f) // 10% chance every check
            {
                if (TriggerInterruption(1f))
                {
                    string interruptionType = ChooseInterruption(1f);
                    stateMachine?.TransitionTo(interruptionType);
                }
            }
        }
        
        private float GetInterruptionMultiplier(float threshold)
        {
            // Higher multiplier for more frustrating thresholds
            return threshold switch
            {
                99f => 3f,  // Very high chance at 99%
                90f => 2f,  // High chance at 90%
                75f => 1.5f,
                50f => 1.2f,
                _ => 1f
            };
        }
        
        private bool TriggerInterruption(float multiplier)
        {
            float totalChance = (errorChance + slowdownChance + rollbackChance + blueScreenChance) * multiplier;
            return Random.Range(0f, 1f) < totalChance;
        }
        
        private string ChooseInterruption(float multiplier)
        {
            float random = Random.Range(0f, 1f);
            float adjustedErrorChance = errorChance * multiplier;
            float adjustedSlowdownChance = slowdownChance * multiplier;
            float adjustedRollbackChance = rollbackChance * multiplier;
            float adjustedBlueScreenChance = blueScreenChance * multiplier;
            
            if (random < adjustedErrorChance)
                return "Error";
            else if (random < adjustedErrorChance + adjustedSlowdownChance)
                return "Slowdown";
            else if (random < adjustedErrorChance + adjustedSlowdownChance + adjustedRollbackChance)
                return "Rollback";
            else
                return "BlueScreen";
        }
        
        /// <summary>
        /// Changes the download speed (can be called by other states).
        /// </summary>
        /// <param name="newSpeed">New download speed</param>
        public void ChangeSpeed(float newSpeed)
        {
            _currentSpeed = newSpeed;
            _downloadManager.StopProgressUpdate();
            _downloadManager.StartProgressUpdate(_currentSpeed);
            
            string speedDescription = newSpeed switch
            {
                var s when s <= slowSpeed => "very slow",
                var s when s <= normalSpeed => "normal",
                var s when s >= fastSpeed => "fast",
                _ => "moderate"
            };
            
            Debug.Log($"Download speed changed to {speedDescription} ({newSpeed})");
        }
    }
}
