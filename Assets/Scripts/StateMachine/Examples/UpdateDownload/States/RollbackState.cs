using UnityEngine;
using StateMachine.Core;

namespace StateMachine.Examples.UpdateDownload.States
{
    /// <summary>
    /// Rollback state where progress mysteriously goes backwards - very frustrating!
    /// </summary>
    public class RollbackState : MonoBehaviour, IState
    {
        [Header("Rollback Settings")]
        [SerializeField] private float rollbackDuration = 3f;
        [SerializeField] private float minRollbackAmount = 10f;
        [SerializeField] private float maxRollbackAmount = 30f;
        [SerializeField] private float rollbackSpeed = 5f;
        
        [Header("Rollback Messages")]
        [SerializeField] private string[] rollbackMessages = {
            "Verifying downloaded files...",
            "Corrupted data detected. Rolling back...",
            "Checksum mismatch. Reverting changes...",
            "File integrity check failed. Undoing progress...",
            "Network error caused corruption. Fixing...",
            "Antivirus quarantined files. Restoring...",
            "Disk error detected. Recovering data...",
            "Update validation failed. Rolling back..."
        };
        
        private float _timer;
        private float _targetProgress;
        private float _startProgress;
        private bool _rollbackComplete;
        private UpdateDownloadStateMachine _downloadManager;
        
        public string StateId => "Rollback";
        
        private void Awake()
        {
            _downloadManager = GetComponent<UpdateDownloadStateMachine>();
        }
        
        public void OnEnter(IStateMachineContext context, object data = null)
        {
            _timer = 0f;
            _rollbackComplete = false;
            
            // Get current progress
            _startProgress = _downloadManager.GetProgress();
            
            // Calculate rollback amount (but don't go below 0)
            float rollbackAmount = Random.Range(minRollbackAmount, maxRollbackAmount);
            _targetProgress = Mathf.Max(0f, _startProgress - rollbackAmount);
            
            // Stop normal progress
            _downloadManager.StopProgressUpdate();
            
            // Choose random rollback message
            string rollbackMessage = rollbackMessages[Random.Range(0, rollbackMessages.Length)];
            _downloadManager.UpdateStatus(rollbackMessage);
            _downloadManager.UpdateSpeed(0f);
            
            Debug.Log($"Rolling back from {_startProgress:F1}% to {_targetProgress:F1}%: {rollbackMessage}");
        }
        
        public void OnUpdate(IStateMachineContext context)
        {
            _timer += Time.deltaTime;
            
            if (!_rollbackComplete)
            {
                // Animate the rollback
                float currentProgress = _downloadManager.GetProgress();
                if (currentProgress > _targetProgress)
                {
                    float newProgress = currentProgress - (rollbackSpeed * Time.deltaTime);
                    newProgress = Mathf.Max(_targetProgress, newProgress);
                    _downloadManager.SetProgress(newProgress);
                    
                    // Show negative "speed" to indicate rollback
                    _downloadManager.UpdateSpeed(-rollbackSpeed * 100f);
                }
                else
                {
                    _rollbackComplete = true;
                    _downloadManager.UpdateSpeed(0f);
                    _downloadManager.UpdateStatus("Rollback complete. Resuming download...");
                }
            }
            
            // After rollback duration, return to downloading
            if (_timer >= rollbackDuration && _rollbackComplete)
            {
                var stateMachine = context.OwnerBehaviour as StateMachine.Unity.StateMachineBehaviour;
                stateMachine?.TransitionTo("Downloading");
            }
        }
        
        public void OnFixedUpdate(IStateMachineContext context)
        {
            // No fixed update needed
        }
        
        public void OnExit(IStateMachineContext context, IState nextState)
        {
            Debug.Log($"Rollback complete. Lost {_startProgress - _targetProgress:F1}% progress. Resuming...");
        }
        
        public bool CanTransitionTo(string targetStateId, IStateMachineContext context)
        {
            // Can only transition back to downloading
            return targetStateId == "Downloading";
        }
        
        /// <summary>
        /// Gets the amount of progress that was rolled back.
        /// </summary>
        /// <returns>Progress lost during rollback</returns>
        public float GetRollbackAmount()
        {
            return _startProgress - _targetProgress;
        }
    }
}
