using UnityEngine;
using StateMachine.Core;
using System.Collections;

namespace StateMachine.Examples.UpdateDownload.States
{
    /// <summary>
    /// Blue Screen of Death state - the ultimate frustration!
    /// </summary>
    public class BlueScreenState : MonoBehaviour, IState
    {
        [Header("Blue Screen Settings")]
        [SerializeField] private float blueScreenDuration = 8f;
        [SerializeField] private float rebootTime = 3f;
        [SerializeField] private string[] blueScreenMessages = {
            "SYSTEM_THREAD_EXCEPTION_NOT_HANDLED",
            "MEMORY_MANAGEMENT",
            "IRQL_NOT_LESS_OR_EQUAL",
            "PAGE_FAULT_IN_NONPAGED_AREA",
            "KERNEL_SECURITY_CHECK_FAILURE",
            "CRITICAL_PROCESS_DIED",
            "SYSTEM_SERVICE_EXCEPTION",
            "DPC_WATCHDOG_VIOLATION"
        };
        
        [Header("Reboot Messages")]
        [SerializeField] private string[] rebootMessages = {
            "Restarting system...",
            "Checking system integrity...",
            "Loading Windows...",
            "Restoring previous session...",
            "Initializing drivers..."
        };
        
        private float _timer;
        private bool _isRebooting;
        private UpdateDownloadStateMachine _downloadManager;
        private Coroutine _rebootCoroutine;
        
        public string StateId => "BlueScreen";
        
        private void Awake()
        {
            _downloadManager = GetComponent<UpdateDownloadStateMachine>();
        }
        
        public void OnEnter(IStateMachineContext context, object data = null)
        {
            _timer = 0f;
            _isRebooting = false;
            
            // Stop all progress
            _downloadManager.StopProgressUpdate();
            _downloadManager.UpdateSpeed(0f);
            
            // Show blue screen
            _downloadManager.ShowBlueScreen(true);
            
            // Choose random blue screen error
            string errorMessage = blueScreenMessages[Random.Range(0, blueScreenMessages.Length)];
            _downloadManager.UpdateStatus($"STOP: 0x0000007E ({errorMessage})");
            
            // Set progress to 0 (system crashed)
            _downloadManager.SetProgress(0f);
            
            Debug.Log($"BLUE SCREEN OF DEATH: {errorMessage}");
        }
        
        public void OnUpdate(IStateMachineContext context)
        {
            _timer += Time.deltaTime;
            
            // After blue screen duration, start "rebooting"
            if (!_isRebooting && _timer >= blueScreenDuration)
            {
                StartReboot(context);
            }
        }
        
        public void OnFixedUpdate(IStateMachineContext context)
        {
            // No fixed update needed
        }
        
        public void OnExit(IStateMachineContext context, IState nextState)
        {
            // Hide blue screen
            _downloadManager.ShowBlueScreen(false);
            
            // Stop reboot coroutine if running
            if (_rebootCoroutine != null)
            {
                StopCoroutine(_rebootCoroutine);
                _rebootCoroutine = null;
            }
            
            Debug.Log("System 'rebooted', exiting blue screen state");
        }
        
        public bool CanTransitionTo(string targetStateId, IStateMachineContext context)
        {
            // Can only transition to restarting after reboot
            return targetStateId == "Restarting";
        }
        
        private void StartReboot(IStateMachineContext context)
        {
            _isRebooting = true;
            _rebootCoroutine = StartCoroutine(RebootSequence(context));
        }
        
        private IEnumerator RebootSequence(IStateMachineContext context)
        {
            // Hide blue screen and show reboot messages
            _downloadManager.ShowBlueScreen(false);
            
            float messageInterval = rebootTime / rebootMessages.Length;
            
            for (int i = 0; i < rebootMessages.Length; i++)
            {
                _downloadManager.UpdateStatus(rebootMessages[i]);
                yield return new WaitForSeconds(messageInterval);
            }
            
            // Transition to restarting state
            var stateMachine = context.OwnerBehaviour as StateMachine.Unity.StateMachineBehaviour;
            stateMachine?.TransitionTo("Restarting");
        }
        
        /// <summary>
        /// Simulates pressing Ctrl+Alt+Del to force reboot (can be called by UI).
        /// </summary>
        public void ForceReboot()
        {
            if (!_isRebooting)
            {
                var context = GetComponent<StateMachine.Unity.StateMachineBehaviour>()?.GetContext();
                if (context != null)
                {
                    StartReboot(context);
                }
            }
        }
    }
}
