using UnityEngine;
using StateMachine.Core;

namespace StateMachine.Examples.UpdateDownload.States
{
    /// <summary>
    /// Initial state that prepares the download and shows various "preparing" messages.
    /// </summary>
    public class InitializingState : MonoBehaviour, IState
    {
        [Header("Initialization Settings")]
        [SerializeField] private float initializationTime = 3f;
        [SerializeField] private string[] initMessages = {
            "Preparing download...",
            "Checking system requirements...",
            "Verifying game files...",
            "Connecting to servers...",
            "Initializing download manager..."
        };
        
        private float _timer;
        private int _currentMessageIndex;
        private float _messageChangeInterval;
        private UpdateDownloadStateMachine _downloadManager;
        
        public string StateId => "Initializing";
        
        private void Awake()
        {
            _downloadManager = GetComponent<UpdateDownloadStateMachine>();
            _messageChangeInterval = initializationTime / initMessages.Length;
        }
        
        public void OnEnter(IStateMachineContext context, object data = null)
        {
            _timer = 0f;
            _currentMessageIndex = 0;
            
            // Reset progress to 0
            _downloadManager.UpdateProgress(0f);
            _downloadManager.UpdateSpeed(0f);
            
            // Show first initialization message
            if (initMessages.Length > 0)
            {
                _downloadManager.UpdateStatus(initMessages[0]);
            }
            
            Debug.Log("Starting download initialization...");
        }
        
        public void OnUpdate(IStateMachineContext context)
        {
            _timer += Time.deltaTime;
            
            // Change messages periodically
            int targetMessageIndex = Mathf.FloorToInt(_timer / _messageChangeInterval);
            if (targetMessageIndex != _currentMessageIndex && targetMessageIndex < initMessages.Length)
            {
                _currentMessageIndex = targetMessageIndex;
                _downloadManager.UpdateStatus(initMessages[_currentMessageIndex]);
            }
            
            // After initialization time, move to downloading
            if (_timer >= initializationTime)
            {
                var stateMachine = context.OwnerBehaviour as StateMachine.Unity.StateMachineBehaviour;
                stateMachine?.TransitionTo("Downloading");
            }
        }
        
        public void OnFixedUpdate(IStateMachineContext context)
        {
            // No fixed update needed
        }
        
        public void OnExit(IStateMachineContext context, IState nextState)
        {
            Debug.Log("Initialization complete, starting download...");
        }
        
        public bool CanTransitionTo(string targetStateId, IStateMachineContext context)
        {
            // Can only transition to downloading state
            return targetStateId == "Downloading";
        }
    }
}
