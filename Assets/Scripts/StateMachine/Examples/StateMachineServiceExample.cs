using Services;
using UnityEngine;
using StateMachine.Services;
using StateMachine.Core;

namespace StateMachine.Examples
{
    /// <summary>
    /// Example script demonstrating how to use the StateMachineService.
    /// </summary>
    public class StateMachineServiceExample : MonoBehaviour
    {
        [Header("Example Controls")]
        [SerializeField] private KeyCode pauseAllKey = KeyCode.P;
        [SerializeField] private KeyCode startEnemiesKey = KeyCode.E;
        [SerializeField] private KeyCode stopUIKey = KeyCode.U;
        [SerializeField] private KeyCode debugToggleKey = KeyCode.F1;
        
        [Header("Service References")]
        [SerializeField] private StateMachineServiceDebugger debugger;

        
        private StateMachineService _stateMachineService;
        
        private StateMachineService StateMachineService
        {
            get
            {
                if (_stateMachineService == null)
                {
                    _stateMachineService = ServiceLocator.Locate<StateMachineService>();
                }
                
                return _stateMachineService;
            }
        }
        
        private void Start()
        {
            // Subscribe to service events
            StateMachineService.OnStateMachineRegistered += HandleStateMachineRegistered;
            StateMachineService.OnStateMachineUnregistered += HandleStateMachineUnregistered;
            StateMachineService.OnAnyStateChanged += HandleAnyStateChanged;
            StateMachineService.OnGlobalPauseChanged += HandleGlobalPauseChanged;
            
            // Get reference to debugger if not assigned
            if (debugger == null)
            {
                debugger = FindObjectOfType<StateMachineServiceDebugger>();
            }
            
            // Example: Register all state machines in scene (usually automatic)
            StateMachineService.RegisterAllStateMachinesInScene();
            
            LogServiceStatus();
        }
        
        private void OnDestroy()
        {
            // Unsubscribe from events
            StateMachineService.OnStateMachineRegistered -= HandleStateMachineRegistered;
            StateMachineService.OnStateMachineUnregistered -= HandleStateMachineUnregistered;
            StateMachineService.OnAnyStateChanged -= HandleAnyStateChanged;
            StateMachineService.OnGlobalPauseChanged -= HandleGlobalPauseChanged;
        }
        
        private void Update()
        {
            HandleInput();
        }
        
        private void HandleInput()
        {
            // Toggle global pause
            if (Input.GetKeyDown(pauseAllKey))
            {
                bool currentPause = StateMachineService.IsGloballyPaused();
                StateMachineService.SetGlobalPause(!currentPause);
                Debug.Log($"Global pause toggled: {!currentPause}");
            }
            
            // Start all enemy state machines
            if (Input.GetKeyDown(startEnemiesKey))
            {
                StateMachineService.StartStateMachinesByTag("Enemy");
                Debug.Log("Started all enemy state machines");
            }
            
            // Stop all UI state machines
            if (Input.GetKeyDown(stopUIKey))
            {
                StateMachineService.StopStateMachinesByTag("UI");
                Debug.Log("Stopped all UI state machines");
            }
            
            // Toggle debug interface
            if (Input.GetKeyDown(debugToggleKey) && debugger != null)
            {
                debugger.ToggleDebugGUI();
            }
        }
        
        private void HandleStateMachineRegistered(string id, IStateMachine stateMachine)
        {
            Debug.Log($"[ServiceExample] State machine registered: {id}");
            
            // Example: Special handling for specific state machines
            if (id.Contains("UpdateDownload"))
            {
                Debug.Log("Update download state machine detected!");
                
                // You could subscribe to specific events or configure it here
                // stateMachine.OnStateChanged += HandleUpdateDownloadStateChange;
            }
        }
        
        private void HandleStateMachineUnregistered(string id, IStateMachine stateMachine)
        {
            Debug.Log($"[ServiceExample] State machine unregistered: {id}");
        }
        
        private void HandleAnyStateChanged(string stateMachineId, IState oldState, IState newState)
        {
            string oldStateName = oldState?.StateId ?? "None";
            string newStateName = newState?.StateId ?? "None";
            
            Debug.Log($"[ServiceExample] {stateMachineId}: {oldStateName} -> {newStateName}");
            
            // Example: React to specific state changes
            if (newStateName == "Error" && stateMachineId.Contains("UpdateDownload"))
            {
                Debug.Log("Update download encountered an error!");
                // Could trigger UI notifications, sound effects, etc.
            }
        }
        
        private void HandleGlobalPauseChanged(bool isPaused)
        {
            Debug.Log($"[ServiceExample] Global pause changed: {isPaused}");
            
            // Example: Update UI, pause audio, etc.
            if (isPaused)
            {
                Time.timeScale = 0f; // Pause game time
            }
            else
            {
                Time.timeScale = 1f; // Resume game time
            }
        }
        
        private void LogServiceStatus()
        {
            var allInfo = StateMachineService.GetAllStateMachineInfo();
            
            Debug.Log($"=== StateMachineService Status ===");
            Debug.Log($"Total registered: {allInfo.Count}");
            Debug.Log($"Currently active: {allInfo.FindAll(info => info.IsActive).Count}");
            Debug.Log($"Globally paused: {StateMachineService.IsGloballyPaused()}");
            
            // Log each state machine
            foreach (var info in allInfo)
            {
                string status = info.IsActive ? "ACTIVE" : "INACTIVE";
                string currentState = info.CurrentState?.StateId ?? "None";
                string tags = string.Join(", ", info.Tags);
                
                Debug.Log($"  [{status}] {info.Id} | State: {currentState} | Tags: [{tags}]");
            }
        }
        
        /// <summary>
        /// Example method to demonstrate programmatic control.
        /// </summary>
        [ContextMenu("Demonstrate Service Features")]
        public void DemonstrateServiceFeatures()
        {
            Debug.Log("=== Demonstrating StateMachineService Features ===");
            
            // 1. Get all state machines
            var allInfo = StateMachineService.GetAllStateMachineInfo();
            Debug.Log($"Found {allInfo.Count} registered state machines");
            
            // 2. Filter by tag
            var playerStateMachines = StateMachineService.GetStateMachinesByTag("Player");
            Debug.Log($"Found {playerStateMachines.Count} player state machines");
            
            // 3. Control specific state machine
            var updateDownloadSM = StateMachineService.GetStateMachine("UpdateDownloadManager");
            if (updateDownloadSM != null)
            {
                Debug.Log($"Update download state machine current state: {updateDownloadSM.CurrentState?.StateId}");
                
                // Force a transition (example)
                if (updateDownloadSM.CurrentState?.StateId == "Downloading")
                {
                    updateDownloadSM.ForceTransitionTo("Error");
                    Debug.Log("Forced update download to error state");
                }
            }
            
            // 4. Batch operations
            Debug.Log("Stopping all UI state machines...");
            StateMachineService.StopStateMachinesByTag("UI");
            
            // Wait a moment, then restart them
            Invoke(nameof(RestartUIStateMachines), 2f);
        }
        
        private void RestartUIStateMachines()
        {
            StateMachineService.StartStateMachinesByTag("UI");
            Debug.Log("Restarted all UI state machines");
        }
        
        /// <summary>
        /// Example method to show how to focus the debugger on a specific state machine.
        /// </summary>
        /// <param name="stateMachineId">The ID of the state machine to focus on</param>
        public void FocusDebuggerOnStateMachine(string stateMachineId)
        {
            if (debugger != null)
            {
                debugger.FocusStateMachine(stateMachineId);
                Debug.Log($"Focused debugger on state machine: {stateMachineId}");
            }
        }
        
        /// <summary>
        /// Example method to demonstrate filtering by tags.
        /// </summary>
        /// <param name="tag">The tag to filter by</param>
        public void FilterDebuggerByTag(string tag)
        {
            if (debugger != null)
            {
                debugger.SetFilterTag(tag);
                Debug.Log($"Set debugger filter to tag: {tag}");
            }
        }
    }
}
