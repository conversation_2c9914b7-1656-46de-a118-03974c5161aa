using System;
using System.Collections.Generic;
using Services;
using UnityEngine;
using StateMachine.Core;
using StateMachine.Services;

namespace StateMachine.Unity
{
    /// <summary>
    /// MonoBehaviour wrapper for the state machine system.
    /// Provides Unity integration and inspector-friendly configuration.
    /// </summary>
    public class StateMachineBehaviour : MonoBehaviour
    {
        [Header("State Machine Configuration")]
        [SerializeField] private string initialStateId = "";
        [SerializeField] private bool startOnAwake = true;
        [SerializeField] private bool debugMode = false;

        [Header("Service Registration")]
        [SerializeField] private bool registerWithService = true;
        [SerializeField] private string serviceId = "";
        [SerializeField] private string[] serviceTags = new string[0];
        
        [Header("State Configuration")]
        [SerializeField] private List<StateConfiguration> stateConfigurations = new List<StateConfiguration>();
        
        // Events
        public event Action<IState, IState> OnStateChanged;
        public event Action<string> OnStateMachineStarted;
        public event Action OnStateMachineStopped;
        
        // Private fields
        private IStateMachine _stateMachine;
        private IStateMachineContext _context;
        private readonly Dictionary<string, IState> _configuredStates = new Dictionary<string, IState>();
        
        // Properties
        public IStateMachine StateMachine => _stateMachine;
        public IState CurrentState => _stateMachine?.CurrentState;
        public IState PreviousState => _stateMachine?.PreviousState;
        public bool IsRunning => _stateMachine?.IsRunning ?? false;
        public float TimeInCurrentState => _stateMachine?.TimeInCurrentState ?? 0f;
        
        private StateMachineService _stateMachineService;
        
        private StateMachineService StateMachineService
        {
            get
            {
                if (_stateMachineService == null)
                {
                    _stateMachineService = ServiceLocator.Locate<StateMachineService>();
                }
                
                return _stateMachineService;
            }
        }
        
        /// <summary>
        /// Configuration class for states in the inspector.
        /// </summary>
        [Serializable]
        public class StateConfiguration
        {
            public string stateId;
            public MonoBehaviour stateComponent;
            public bool isInitialState;
        }
        
        private void Awake()
        {
            InitializeStateMachine();

            // Register with service if enabled
            if (registerWithService)
            {
                RegisterWithService();
            }

            if (startOnAwake && !string.IsNullOrEmpty(initialStateId))
            {
                StartStateMachine(initialStateId);
            }
        }
        
        private void Update()
        {
            _stateMachine?.Update();
        }
        
        private void FixedUpdate()
        {
            _stateMachine?.FixedUpdate();
        }
        
        private void OnDestroy()
        {
            // Unregister from service
            if (registerWithService)
            {
                UnregisterFromService();
            }

            if (_stateMachine != null)
            {
                _stateMachine.OnStateChanged -= HandleStateChanged;
                _stateMachine.Stop();
            }
        }
        
        /// <summary>
        /// Initializes the state machine and configures states.
        /// </summary>
        private void InitializeStateMachine()
        {
            // Create context and state machine
            _context = new StateMachineContext(gameObject, this, null);
            _stateMachine = new Core.StateMachine(_context);
            
            // Update context with the actual state machine reference
            _context = new StateMachineContext(gameObject, this, _stateMachine);
            _stateMachine = new Core.StateMachine(_context);
            
            // Subscribe to state change events
            _stateMachine.OnStateChanged += HandleStateChanged;
            
            // Configure states from inspector
            ConfigureStatesFromInspector();
            
            // Find initial state if not set
            if (string.IsNullOrEmpty(initialStateId))
            {
                var initialConfig = stateConfigurations.Find(config => config.isInitialState);
                if (initialConfig != null)
                {
                    initialStateId = initialConfig.stateId;
                }
            }
        }
        
        /// <summary>
        /// Configures states based on inspector settings.
        /// </summary>
        private void ConfigureStatesFromInspector()
        {
            foreach (var config in stateConfigurations)
            {
                if (string.IsNullOrEmpty(config.stateId))
                {
                    Debug.LogWarning($"State configuration has empty ID on {gameObject.name}");
                    continue;
                }
                
                IState state = null;
                
                // Try to get state from component
                if (config.stateComponent != null && config.stateComponent is IState)
                {
                    state = config.stateComponent as IState;
                }
                else if (config.stateComponent != null)
                {
                    // Try to find IState interface on the component
                    var interfaces = config.stateComponent.GetType().GetInterfaces();
                    foreach (var iface in interfaces)
                    {
                        if (iface == typeof(IState))
                        {
                            state = config.stateComponent as IState;
                            break;
                        }
                    }
                }
                
                if (state != null)
                {
                    AddState(state);
                    _configuredStates[config.stateId] = state;
                }
                else
                {
                    Debug.LogWarning($"Could not create state '{config.stateId}' from component on {gameObject.name}");
                }
            }
        }
        
        /// <summary>
        /// Handles state change events.
        /// </summary>
        private void HandleStateChanged(IState oldState, IState newState)
        {
            if (debugMode)
            {
                string oldStateName = oldState?.StateId ?? "None";
                string newStateName = newState?.StateId ?? "None";
                Debug.Log($"[{gameObject.name}] State changed: {oldStateName} -> {newStateName}");
            }
            
            OnStateChanged?.Invoke(oldState, newState);
        }
        
        /// <summary>
        /// Adds a state to the state machine.
        /// </summary>
        /// <param name="state">The state to add</param>
        public void AddState(IState state)
        {
            _stateMachine?.AddState(state);
        }
        
        /// <summary>
        /// Removes a state from the state machine.
        /// </summary>
        /// <param name="stateId">The ID of the state to remove</param>
        /// <returns>True if the state was removed</returns>
        public bool RemoveState(string stateId)
        {
            return _stateMachine?.RemoveState(stateId) ?? false;
        }
        
        /// <summary>
        /// Gets a state by its ID.
        /// </summary>
        /// <param name="stateId">The state ID</param>
        /// <returns>The state, or null if not found</returns>
        public IState GetState(string stateId)
        {
            return _stateMachine?.GetState(stateId);
        }
        
        /// <summary>
        /// Starts the state machine with the specified initial state.
        /// </summary>
        /// <param name="initialState">The ID of the initial state</param>
        /// <param name="data">Optional data to pass to the initial state</param>
        public void StartStateMachine(string initialState = null, object data = null)
        {
            string stateToStart = initialState ?? initialStateId;
            
            if (string.IsNullOrEmpty(stateToStart))
            {
                Debug.LogError($"Cannot start state machine on {gameObject.name}: No initial state specified");
                return;
            }
            
            _stateMachine?.Start(stateToStart, data);
            OnStateMachineStarted?.Invoke(stateToStart);
        }
        
        /// <summary>
        /// Stops the state machine.
        /// </summary>
        public void StopStateMachine()
        {
            _stateMachine?.Stop();
            OnStateMachineStopped?.Invoke();
        }
        
        /// <summary>
        /// Transitions to a new state.
        /// </summary>
        /// <param name="targetStateId">The ID of the target state</param>
        /// <param name="data">Optional data to pass to the new state</param>
        /// <returns>True if the transition was successful</returns>
        public bool TransitionTo(string targetStateId, object data = null)
        {
            return _stateMachine?.TransitionTo(targetStateId, data) ?? false;
        }
        
        /// <summary>
        /// Forces a transition without checking conditions.
        /// </summary>
        /// <param name="targetStateId">The ID of the target state</param>
        /// <param name="data">Optional data to pass to the new state</param>
        /// <returns>True if the transition was successful</returns>
        public bool ForceTransitionTo(string targetStateId, object data = null)
        {
            return _stateMachine?.ForceTransitionTo(targetStateId, data) ?? false;
        }
        
        /// <summary>
        /// Gets the state machine context.
        /// </summary>
        /// <returns>The state machine context</returns>
        public IStateMachineContext GetContext()
        {
            return _context;
        }

        /// <summary>
        /// Registers this state machine with the StateMachineService.
        /// </summary>
        private void RegisterWithService()
        {
            string id = string.IsNullOrEmpty(serviceId) ? $"{gameObject.name}_{GetInstanceID()}" : serviceId;
            string[] tags = serviceTags.Length > 0 ? serviceTags : new string[] { gameObject.tag };

            StateMachineService.RegisterStateMachine(id, _stateMachine, this, tags);
        }

        /// <summary>
        /// Unregisters this state machine from the StateMachineService.
        /// </summary>
        private void UnregisterFromService()
        {
            string id = string.IsNullOrEmpty(serviceId) ? $"{gameObject.name}_{GetInstanceID()}" : serviceId;
            StateMachineService.UnregisterStateMachine(id);
        }

        /// <summary>
        /// Gets the service ID for this state machine.
        /// </summary>
        /// <returns>The service ID</returns>
        public string GetServiceId()
        {
            return string.IsNullOrEmpty(serviceId) ? $"{gameObject.name}_{GetInstanceID()}" : serviceId;
        }

        /// <summary>
        /// Sets the service ID for this state machine.
        /// </summary>
        /// <param name="id">The new service ID</param>
        public void SetServiceId(string id)
        {
            bool wasRegistered = registerWithService && _stateMachine != null;

            if (wasRegistered)
            {
                UnregisterFromService();
            }

            serviceId = id;

            if (wasRegistered)
            {
                RegisterWithService();
            }
        }
    }
}
