using UnityEngine;

namespace Data
{
    [CreateAssetMenu(menuName = "Variables/Ranged Int")]
    public class RangedIntVariable : RangedScriptableVariable<int>
    {
        [SerializeField] private int Min = 0;
        [SerializeField] private int Max = 1;

        public override int Value
        {
            get => value;
            set
            {
                base.value = Mathf.Clamp(value, Min, Max);
                OnValueChanged?.Invoke(base.value);
            }
        }
    }
}