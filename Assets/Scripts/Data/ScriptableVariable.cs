using System;
using UnityEngine;

namespace Data
{
    public abstract class ScriptableVariable<T> : ScriptableObject
    {
        [SerializeField] private T value;
        public Action<T> OnValueChanged;

        public virtual T Value
        {
            get => value;
            set
            {
                this.value = value;
                OnValueChanged?.Invoke(this.value);
            }
        }
    }
}