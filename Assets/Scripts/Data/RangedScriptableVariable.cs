using System;
using UnityEngine;

namespace Data
{
    public abstract class RangedScriptableVariable<T> : ScriptableObject
    {
        [SerializeField] protected T value;
        public Action<T> OnValueChanged;
        
        public virtual T Value
        {
            get => value;
            set
            {
                this.value = value;
                OnValueChanged?.Invoke(this.value);
            }
        }
    }
}