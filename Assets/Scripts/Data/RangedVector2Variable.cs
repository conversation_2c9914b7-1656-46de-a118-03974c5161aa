using UnityEngine;

namespace Data
{
    [CreateAssetMenu(menuName = "Variables/Ranged Vector2")]
    public class RangedVector2Variable : RangedScriptableVariable<Vector2>
    {
        [SerializeField] private Vector2 Min = Vector2.zero;
        [SerializeField] private Vector2 Max = Vector2.one;

        public override Vector2 Value
        {
            get => value;
            set
            {
                this.value = new Vector2(Mathf.Clamp(value.x, Min.x, Max.x), Mathf.Clamp(value.y, Min.y, Max.y));
                OnValueChanged?.Invoke(this.value);
            }
        }
    }
}