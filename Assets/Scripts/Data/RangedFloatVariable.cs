using UnityEngine;

namespace Data
{
    [CreateAssetMenu(menuName = "Variables/Ranged Float")]
    public class RangedFloatVariable : RangedScriptableVariable<float>
    {
        [SerializeField] private float Min = 0;
        [SerializeField] private float Max = 1;

        public override float Value
        {
            get => value;
            set
            {
                base.value = Mathf.Clamp(value, Min, Max);
                OnValueChanged?.Invoke(base.value);
            }
        }
    }
}