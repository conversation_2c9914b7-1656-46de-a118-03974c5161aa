using UnityEngine;

public class RollingChairAudioPlayer : MonoBehaviour
{
    private Rigidbody _rigidbody;
    private AudioSource _audioSource;

    [SerializeField] private float MinimumRollingSpeedForAudio = 0.5f;
    [SerializeField] private float _velocityToVolumeRatio = 10.0f;

    private void Awake()
    {
        _rigidbody = GetComponentInParent<Rigidbody>();
        _audioSource = GetComponent<AudioSource>();
        
        // Configure AudioSource
        _audioSource.playOnAwake = true;
        _audioSource.volume = 0.0f;
    }

    private void Update()
    {
        // raycast to see if we are touching the ground
        var grounded = Physics.Raycast(transform.position + new Vector3(-0.33f, 0, 0.33f), -transform.up, 0.2f);
        if (!grounded)
        {
            // lerp the volume to 0
            var time = Time.deltaTime * 5f;
            _audioSource.volume = Mathf.Lerp(_audioSource.volume, 0, time);
            _audioSource.pitch = Mathf.Lerp(_audioSource.pitch, 1, time);
            return;
        }
        
        var currentVelocity = _rigidbody.linearVelocity.magnitude;

        if (currentVelocity < MinimumRollingSpeedForAudio)
        {
            return;
        }

        _audioSource.volume = Mathf.Clamp01(currentVelocity / _velocityToVolumeRatio);
        _audioSource.pitch = Mathf.Clamp01(currentVelocity / _velocityToVolumeRatio) * 0.25f + 0.75f;
    }
}
