using UnityEngine;
using UnityEngine.Audio;

/// <summary>
/// Plays background music through the "Ambience" channel of an AudioMixer.
/// Automatically loops the assigned AudioClip on start.
/// </summary>
public class AmbientMusicPlayer : MonoBehaviour
{
    [Header("Audio Settings")]
    [Tooltip("The AudioMixer asset containing the Ambience group")]
    public AudioMixer audioMixer;
    
    [Tooltip("The name of the Ambience group in the AudioMixer")]
    public string ambienceGroupName = "Ambience";
    
    [Tooltip("The AudioClip to play")]
    public AudioClip AudioClip;
    
    [Tooltip("Volume level (0-1)")]
    [Range(0f, 1f)]
    public float volume = 1f;
    
    [Tooltip("Should the Ambience start playing automatically?")]
    public bool playOnStart = true;
    
    [Tooltip("Should the Ambience loop?")]
    public bool loop = true;
    
    [Tooltip("The exposed parameter name for volume in the AudioMixer")]
    public string volumeParameterName = "AmbienceVolume";

    private AudioSource _audioSource;
    private AudioMixerGroup _ambienceGroup;

    private void Awake()
    {
        // Create AudioSource component if it doesn't exist
        _audioSource = GetComponent<AudioSource>();
        if (_audioSource == null)
        {
            _audioSource = gameObject.AddComponent<AudioSource>();
        }
        
        // Configure AudioSource
        _audioSource.playOnAwake = false;
        _audioSource.loop = loop;
        _audioSource.clip = AudioClip;
        _audioSource.volume = volume;
        
        // Try to find and assign the Ambience mixer group
        if (audioMixer != null)
        {
            AudioMixerGroup[] groups = audioMixer.FindMatchingGroups(ambienceGroupName);
            if (groups.Length > 0)
            {
                _ambienceGroup = groups[0];
                _audioSource.outputAudioMixerGroup = _ambienceGroup;
                
                // Set initial volume
                SetVolume(volume);
            }
            else
            {
                Debug.LogWarning($"AmbiencePlayer: Could not find AudioMixerGroup named '{ambienceGroupName}'");
            }
        }
        else
        {
            Debug.LogWarning("AmbiencePlayer: No AudioMixer assigned");
        }
    }

    private void Start()
    {
        if (playOnStart && AudioClip != null)
        {
            Play();
        }
    }

    /// <summary>
    /// Plays the audio clip
    /// </summary>
    public void Play()
    {
        if (AudioClip != null && _audioSource != null)
        {
            _audioSource.Play();
        }
        else if (AudioClip == null)
        {
            Debug.LogWarning("AmbiencePlayer: No audio clip assigned");
        }
    }

    /// <summary>
    /// Pauses the music
    /// </summary>
    public void Pause()
    {
        if (_audioSource != null)
        {
            _audioSource.Pause();
        }
    }

    /// <summary>
    /// Stops the music
    /// </summary>
    public void Stop()
    {
        if (_audioSource != null)
        {
            _audioSource.Stop();
        }
    }

    /// <summary>
    /// Sets the volume level (0-1)
    /// </summary>
    /// <param name="newVolume">Volume level between 0 and 1</param>
    public void SetVolume(float newVolume)
    {
        volume = Mathf.Clamp01(newVolume);
        
        // Set the volume on the AudioSource
        if (_audioSource != null)
        {
            _audioSource.volume = volume;
        }
        
        // Also set the volume parameter on the AudioMixer if available
        if (audioMixer != null && !string.IsNullOrEmpty(volumeParameterName))
        {
            // Convert to decibels for the mixer
            // -80dB is silence, 0dB is full volume
            float mixerVolume = volume > 0 ? Mathf.Log10(volume) * 20 : -80f;
            audioMixer.SetFloat(volumeParameterName, mixerVolume);
        }
    }

    /// <summary>
    /// Changes the audio clip
    /// </summary>
    /// <param name="newClip">The new AudioClip to play</param>
    /// <param name="playImmediately">Whether to start playing the new clip immediately</param>
    public void ChangeAudioClip(AudioClip newClip, bool playImmediately = true)
    {
        if (_audioSource != null)
        {
            bool wasPlaying = _audioSource.isPlaying;
            _audioSource.Stop();
            _audioSource.clip = newClip;
            AudioClip = newClip;
            
            if (playImmediately || wasPlaying)
            {
                _audioSource.Play();
            }
        }
    }
}
