using UnityEngine;

/// <summary>
/// Plays sounds when physics objects collide, with volume based on collision velocity.
/// </summary>
[RequireComponent(typeof(Rigidbody))]
public class CollisionSoundPlayer : MonoBehaviour
{
    [Header("Audio Settings")]
    [Tooltip("Audio clips to play on collision (one will be chosen randomly)")]
    public AudioClip[] collisionSounds;
    
    [Tooltip("Minimum volume for collision sounds")]
    [Range(0f, 1f)]
    public float minVolume = 0.1f;
    
    [Tooltip("Maximum volume for collision sounds")]
    [Range(0f, 1f)]
    public float maxVolume = 1.0f;
    
    [Tooltip("Velocity required for minimum volume")]
    public float minVelocityThreshold = 0.5f;
    
    [Tooltip("Velocity at which maximum volume is reached")]
    public float maxVelocityThreshold = 5.0f;
    
    [Tooltip("Cooldown between collision sounds (seconds)")]
    public float soundCooldown = 0.1f;
    
    [Header("Pitch Settings")]
    [Tooltip("Minimum random pitch offset")]
    [Range(-3f, 3f)]
    public float minPitchOffset = -0.1f;
    
    [Toolt<PERSON>("Maximum random pitch offset")]
    [Range(-3f, 3f)]
    public float maxPitchOffset = 0.1f;
    
    [Header("Audio Mixer")]
    [Tooltip("Optional AudioMixerGroup to route sounds through")]
    public UnityEngine.Audio.AudioMixerGroup outputAudioMixerGroup;

    private AudioSource _audioSource;
    private Rigidbody _rigidbody;
    private float _lastSoundTime;

    private void Awake()
    {
        // Get or create AudioSource
        _audioSource = GetComponent<AudioSource>();
        if (_audioSource == null)
        {
            _audioSource = gameObject.AddComponent<AudioSource>();
        }
        
        // Configure AudioSource
        _audioSource.playOnAwake = false;
        _audioSource.spatialBlend = 1.0f; // 3D sound
        
        // Assign mixer group if provided
        if (outputAudioMixerGroup != null)
        {
            _audioSource.outputAudioMixerGroup = outputAudioMixerGroup;
        }
        
        // Get Rigidbody
        _rigidbody = GetComponent<Rigidbody>();
        if (_rigidbody == null)
        {
            _rigidbody = GetComponentInChildren<Rigidbody>();
        }
    }

    private void OnCollisionEnter(Collision collision)
    {
        // Check if we have sounds to play and if cooldown has elapsed
        if (collisionSounds == null || collisionSounds.Length == 0 || Time.time - _lastSoundTime < soundCooldown)
        {
            return;
        }
        
        // Calculate relative velocity magnitude
        float relativeVelocity = collision.relativeVelocity.magnitude;
        
        // Only play sound if velocity is above minimum threshold
        if (relativeVelocity >= minVelocityThreshold)
        {
            // Calculate volume based on velocity, clamped between min and max
            float normalizedVelocity = Mathf.InverseLerp(minVelocityThreshold, maxVelocityThreshold, relativeVelocity);
            float volume = Mathf.Lerp(minVolume, maxVolume, normalizedVelocity);
            
            // Get a random sound from the array
            AudioClip soundToPlay = GetRandomSound();
            
            if (soundToPlay != null)
            {
                // Play the sound with random pitch
                PlayCollisionSound(soundToPlay, volume);
                
                // Update last sound time
                _lastSoundTime = Time.time;
            }
        }
    }
    
    /// <summary>
    /// Gets a random sound from the collision sounds array
    /// </summary>
    private AudioClip GetRandomSound()
    {
        if (collisionSounds == null || collisionSounds.Length == 0)
        {
            return null;
        }
        
        return collisionSounds[Random.Range(0, collisionSounds.Length)];
    }

    /// <summary>
    /// Plays the collision sound at the specified volume with random pitch
    /// </summary>
    /// <param name="sound">The audio clip to play</param>
    /// <param name="volume">Volume level between 0 and 1</param>
    private void PlayCollisionSound(AudioClip sound, float volume)
    {
        if (_audioSource != null && sound != null)
        {
            // Apply random pitch offset
            float randomPitch = 1.0f + Random.Range(minPitchOffset, maxPitchOffset);
            
            _audioSource.pitch = randomPitch;
            _audioSource.clip = sound;
            _audioSource.volume = volume;
            _audioSource.Play();
        }
    }
    
    public void PlayRandomCollisionSound(float volume = 0.5f)
    {
        if (collisionSounds == null || collisionSounds.Length == 0)
        {
            return;
        }
        
        // Get a random sound from the array
        AudioClip soundToPlay = GetRandomSound();
        
        if (soundToPlay != null)
        {
            // Play the sound with random pitch
            PlayCollisionSound(soundToPlay, volume);
        }
    }
}
