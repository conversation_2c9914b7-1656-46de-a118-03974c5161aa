using UnityEngine;
using UnityEngine.Audio;

/// <summary>
/// A flexible audio player that can be used for music, ambience, or any other audio needs.
/// Connects to an AudioMixerGroup and automatically loops the assigned AudioClip on start.
/// </summary>
public class MusicPlayer : MonoBehaviour
{
    [Header("Audio Settings")]
    [Tooltip("The AudioMixerGroup to output audio through")]
    public AudioMixerGroup outputAudioMixerGroup;
    
    [Tooltip("The AudioClip to play")]
    public AudioClip audioClip;
    
    [Tooltip("Volume level (0-1)")]
    [Range(0f, 1f)]
    public float volume = 1f;
    
    [Tooltip("Should the audio start playing automatically?")]
    public bool playOnStart = true;
    
    [Tooltip("Should the audio loop?")]
    public bool loop = true;
    
    [Tooltip("The exposed parameter name for volume in the AudioMixer (optional)")]
    public string volumeParameterName = "";

    private AudioSource _audioSource;

    private void Awake()
    {
        // Create AudioSource component if it doesn't exist
        _audioSource = GetComponent<AudioSource>();
        if (_audioSource == null)
        {
            _audioSource = gameObject.AddComponent<AudioSource>();
        }
        
        // Configure AudioSource
        _audioSource.playOnAwake = false;
        _audioSource.loop = loop;
        _audioSource.clip = audioClip;
        _audioSource.volume = volume;
        
        // Assign the AudioMixerGroup directly
        if (outputAudioMixerGroup != null)
        {
            _audioSource.outputAudioMixerGroup = outputAudioMixerGroup;
            
            // Set initial volume
            SetVolume(volume);
        }
    }

    private void Start()
    {
        if (playOnStart && audioClip != null)
        {
            Play();
        }
    }

    /// <summary>
    /// Plays the audio clip
    /// </summary>
    public void Play()
    {
        if (audioClip != null && _audioSource != null)
        {
            _audioSource.Play();
        }
        else if (audioClip == null)
        {
            Debug.LogWarning("Music Player: No audio clip assigned");
        }
    }

    /// <summary>
    /// Pauses the music
    /// </summary>
    public void Pause()
    {
        if (_audioSource != null)
        {
            _audioSource.Pause();
        }
    }

    /// <summary>
    /// Stops the music
    /// </summary>
    public void Stop()
    {
        if (_audioSource != null)
        {
            _audioSource.Stop();
        }
    }

    /// <summary>
    /// Sets the volume level (0-1)
    /// </summary>
    /// <param name="newVolume">Volume level between 0 and 1</param>
    public void SetVolume(float newVolume)
    {
        volume = Mathf.Clamp01(newVolume);
        
        // Set the volume on the AudioSource
        if (_audioSource != null)
        {
            _audioSource.volume = volume;
        }
        
        // Also set the volume parameter on the AudioMixer if available
        if (outputAudioMixerGroup != null && !string.IsNullOrEmpty(volumeParameterName))
        {
            // Get the AudioMixer from the group
            AudioMixer mixer = outputAudioMixerGroup.audioMixer;
            if (mixer != null)
            {
                // Convert to decibels for the mixer
                // -80dB is silence, 0dB is full volume
                float mixerVolume = volume > 0 ? Mathf.Log10(volume) * 20 : -80f;
                mixer.SetFloat(volumeParameterName, mixerVolume);
            }
        }
    }

    /// <summary>
    /// Changes the audio clip
    /// </summary>
    /// <param name="newClip">The new AudioClip to play</param>
    /// <param name="playImmediately">Whether to start playing the new clip immediately</param>
    public void ChangeAudioClip(AudioClip newClip, bool playImmediately = true)
    {
        if (_audioSource != null)
        {
            bool wasPlaying = _audioSource.isPlaying;
            _audioSource.Stop();
            _audioSource.clip = newClip;
            audioClip = newClip;
            
            if (playImmediately || wasPlaying)
            {
                _audioSource.Play();
            }
        }
    }
    
    /// <summary>
    /// Changes the AudioMixerGroup at runtime
    /// </summary>
    /// <param name="newMixerGroup">The new AudioMixerGroup to use</param>
    public void ChangeAudioMixerGroup(AudioMixerGroup newMixerGroup)
    {
        if (_audioSource != null)
        {
            outputAudioMixerGroup = newMixerGroup;
            _audioSource.outputAudioMixerGroup = newMixerGroup;
        }
    }
}
