using System;
using Data;
using UnityEngine;
using UnityEngine.Serialization;

namespace AreWeWatching
{
    public class MonitorChecker : MonoBehaviour
    {
        [SerializeField] private BoolVariable CanWeSeeTheScreen;

        private Camera _mainCamera;
        private Renderer _renderer;
        
        private void Awake()
        {
            _mainCamera = Camera.main;
            _renderer = GetComponentInChildren<Renderer>();
        }

        private void Update()
        {
            UpdateCanWeSeeTheScreen();
        }

        private void UpdateCanWeSeeTheScreen()
        {
            var isRendererVisible = IsRendererOnScreen(_mainCamera, _renderer);
            var screenForward = -transform.forward;
            
            // check if the camera is facing the same direction as the screen
            var isFacingTheScreenFace = Vector3.Dot(_mainCamera.transform.forward, screenForward) > 0;
            
            CanWeSeeTheScreen.Value = isRendererVisible && isFacingTheScreenFace;
        }

        private bool IsRendererOnScreen(Camera cam, Renderer renderer)
        {
            Plane[] planes = GeometryUtility.CalculateFrustumPlanes(cam);
            return GeometryUtility.TestPlanesAABB(planes, renderer.bounds);
        }
    }
}